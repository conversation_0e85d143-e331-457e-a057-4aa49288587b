#!/usr/bin/env python3
"""
Test script to verify channel post filtering logic
"""

import asyncio
from unittest.mock import Mock, AsyncMock
from telegram import Update
from telegram.ext import ContextTypes

# Mock the logger to avoid import issues
import logging
logger = logging.getLogger(__name__)

class MockFinalBotApp:
    """Mock version of FinalBotApp to test channel post filtering"""
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages - only process private user messages"""
        try:
            # Skip channel posts and group messages
            if update.channel_post or update.edited_channel_post:
                logger.debug(f"Ignoring channel post from channel: {update.channel_post.chat.id if update.channel_post else update.edited_channel_post.chat.id}")
                return "IGNORED_CHANNEL_POST"
            
            # Skip group messages
            if update.message and update.message.chat.type in ['group', 'supergroup']:
                logger.debug(f"Ignoring group message from chat: {update.message.chat.id}")
                return "IGNORED_GROUP_MESSAGE"
            
            # Ensure we have a valid user and message
            if not update.effective_user or not update.message or not update.message.text:
                logger.debug("Ignoring update without valid user or message text")
                return "IGNORED_INVALID_UPDATE"
            
            # If we get here, it's a valid private message
            return "PROCESSED_PRIVATE_MESSAGE"
            
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            return f"ERROR: {e}"

    async def handle_photo(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle photo uploads - only process private user messages"""
        try:
            # Skip channel posts and group messages
            if update.channel_post or update.edited_channel_post:
                logger.debug(f"Ignoring channel post photo from channel: {update.channel_post.chat.id if update.channel_post else update.edited_channel_post.chat.id}")
                return "IGNORED_CHANNEL_POST_PHOTO"
            
            # Skip group messages
            if update.message and update.message.chat.type in ['group', 'supergroup']:
                logger.debug(f"Ignoring group photo from chat: {update.message.chat.id}")
                return "IGNORED_GROUP_PHOTO"
            
            # Ensure we have a valid user and message with photo
            if not update.effective_user or not update.message or not update.message.photo:
                logger.debug("Ignoring update without valid user or photo")
                return "IGNORED_INVALID_PHOTO_UPDATE"
            
            # If we get here, it's a valid private photo
            return "PROCESSED_PRIVATE_PHOTO"
            
        except Exception as e:
            logger.error(f"Error handling photo: {e}")
            return f"ERROR: {e}"

async def test_channel_post_filtering():
    """Test that channel posts are properly filtered out"""
    bot = MockFinalBotApp()
    context = Mock()
    
    print("Testing channel post filtering...")
    
    # Test 1: Channel post with text
    print("\n1. Testing channel post with text:")
    channel_chat = Mock()
    channel_chat.id = -1002414699235
    channel_chat.type = 'channel'
    
    channel_post = Mock()
    channel_post.chat = channel_chat
    channel_post.text = "Test channel post"
    
    update = Mock()
    update.channel_post = channel_post
    update.edited_channel_post = None
    update.message = None
    update.effective_user = None
    
    result = await bot.handle_message(update, context)
    print(f"Result: {result}")
    assert result == "IGNORED_CHANNEL_POST", f"Expected IGNORED_CHANNEL_POST, got {result}"
    
    # Test 2: Private message (should be processed)
    print("\n2. Testing private message:")
    private_chat = Mock()
    private_chat.id = 12345
    private_chat.type = 'private'
    
    user = Mock()
    user.id = 12345
    user.username = "testuser"
    
    private_message = Mock()
    private_message.chat = private_chat
    private_message.text = "Hello bot"
    
    update = Mock()
    update.channel_post = None
    update.edited_channel_post = None
    update.message = private_message
    update.effective_user = user
    
    result = await bot.handle_message(update, context)
    print(f"Result: {result}")
    assert result == "PROCESSED_PRIVATE_MESSAGE", f"Expected PROCESSED_PRIVATE_MESSAGE, got {result}"
    
    # Test 3: Group message (should be ignored)
    print("\n3. Testing group message:")
    group_chat = Mock()
    group_chat.id = -123456789
    group_chat.type = 'group'
    
    group_message = Mock()
    group_message.chat = group_chat
    group_message.text = "Group message"
    
    update = Mock()
    update.channel_post = None
    update.edited_channel_post = None
    update.message = group_message
    update.effective_user = user
    
    result = await bot.handle_message(update, context)
    print(f"Result: {result}")
    assert result == "IGNORED_GROUP_MESSAGE", f"Expected IGNORED_GROUP_MESSAGE, got {result}"
    
    # Test 4: Channel post with photo
    print("\n4. Testing channel post with photo:")
    channel_post_photo = Mock()
    channel_post_photo.chat = channel_chat
    channel_post_photo.photo = [Mock()]
    
    update = Mock()
    update.channel_post = channel_post_photo
    update.edited_channel_post = None
    update.message = None
    update.effective_user = None
    
    result = await bot.handle_photo(update, context)
    print(f"Result: {result}")
    assert result == "IGNORED_CHANNEL_POST_PHOTO", f"Expected IGNORED_CHANNEL_POST_PHOTO, got {result}"
    
    # Test 5: Private photo (should be processed)
    print("\n5. Testing private photo:")
    private_photo_message = Mock()
    private_photo_message.chat = private_chat
    private_photo_message.photo = [Mock()]
    
    update = Mock()
    update.channel_post = None
    update.edited_channel_post = None
    update.message = private_photo_message
    update.effective_user = user
    
    result = await bot.handle_photo(update, context)
    print(f"Result: {result}")
    assert result == "PROCESSED_PRIVATE_PHOTO", f"Expected PROCESSED_PRIVATE_PHOTO, got {result}"
    
    print("\n✅ All tests passed! Channel post filtering is working correctly.")

if __name__ == "__main__":
    asyncio.run(test_channel_post_filtering())
