#!/usr/bin/env python3
"""
Setup script for the comprehensive task management system
"""

import asyncio
import logging
from datetime import datetime, timezone

from config import Config
from src.database import Database
from src.models.task import Task, TaskType, VerificationMode
from src.services.task_service import TaskService

# Setup logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def setup_task_system():
    """Setup the task management system"""
    try:
        logger.info("🚀 Setting up Task Management System...")
        
        # Initialize database
        database = Database()
        await database.connect()
        logger.info("✅ Database connected")
        
        # Initialize task service
        task_service = TaskService(database.db)
        logger.info("✅ Task service initialized")
        
        # Check if we have admin users configured
        if not Config.ADMIN_USER_IDS:
            logger.warning("⚠️ No admin users configured in ADMIN_USER_IDS")
            logger.info("Please add admin user IDs to your environment variables")
            return
        
        admin_id = Config.ADMIN_USER_IDS[0]
        logger.info(f"Using admin ID: {admin_id}")
        
        # Create sample tasks for demonstration
        logger.info("Creating sample tasks...")
        
        # Sample Join Channel Task
        join_task = await task_service.create_task(
            admin_id=admin_id,
            task_name="Join Our Official Channel",
            task_type=TaskType.JOIN_CHANNEL,
            reward_amount=25.0,
            description="Join our official Telegram channel for updates and announcements",
            channel_id="@your_channel_here",  # Replace with your actual channel
            join_link="https://t.me/your_channel_here"  # Replace with your actual link
        )
        
        if join_task:
            logger.info(f"✅ Created join channel task: {join_task.task_id}")
        else:
            logger.error("❌ Failed to create join channel task")
        
        # Sample Image Submission Task
        image_task = await task_service.create_task(
            admin_id=admin_id,
            task_name="Share Bot Screenshot",
            task_type=TaskType.SUBMIT_IMAGE,
            reward_amount=50.0,
            description="Share a screenshot of the bot in your status or story",
            instructions="Take a screenshot showing the bot interface and post it to your Telegram status or story. Then submit a screenshot of your post here.",
            verification_mode=VerificationMode.MANUAL_REVIEW
        )
        
        if image_task:
            logger.info(f"✅ Created image submission task: {image_task.task_id}")
        else:
            logger.error("❌ Failed to create image submission task")
        
        # Get task statistics
        stats = await task_service.get_task_statistics()
        logger.info(f"📊 Task Statistics: {stats}")
        
        # Close database connection
        database.client.close()
        logger.info("✅ Database connection closed")
        
        logger.info("🎉 Task Management System setup complete!")
        logger.info("\n" + "="*50)
        logger.info("NEXT STEPS:")
        logger.info("1. Update the channel ID and link in the join task")
        logger.info("2. Start your bot with: python final_bot.py")
        logger.info("3. Use /admin command to access task management")
        logger.info("4. Create custom tasks for your users")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"❌ Setup failed: {e}")
        raise

async def verify_system():
    """Verify the task management system is working"""
    try:
        logger.info("🔍 Verifying Task Management System...")
        
        # Initialize database
        database = Database()
        await database.connect()
        
        # Check collections exist
        collections = await database.db.list_collection_names()
        required_collections = ['tasks', 'user_tasks', 'task_submissions']
        
        for collection in required_collections:
            if collection in collections:
                logger.info(f"✅ Collection '{collection}' exists")
            else:
                logger.warning(f"⚠️ Collection '{collection}' not found")
        
        # Check indexes
        for collection_name in required_collections:
            collection = database.db[collection_name]
            indexes = await collection.list_indexes().to_list(None)
            logger.info(f"📋 {collection_name} has {len(indexes)} indexes")
        
        # Initialize task service
        task_service = TaskService(database.db)
        
        # Get active tasks
        active_tasks = await task_service.get_active_tasks()
        logger.info(f"📋 Found {len(active_tasks)} active tasks")
        
        for task in active_tasks:
            logger.info(f"  - {task.task_name} ({task.task_type.value}) - 💎{task.reward_amount}")
        
        # Get statistics
        stats = await task_service.get_task_statistics()
        logger.info(f"📊 System Statistics: {stats}")
        
        # Close database connection
        database.client.close()
        logger.info("✅ Verification complete")
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        raise

async def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "verify":
        await verify_system()
    else:
        await setup_task_system()

if __name__ == "__main__":
    asyncio.run(main())
