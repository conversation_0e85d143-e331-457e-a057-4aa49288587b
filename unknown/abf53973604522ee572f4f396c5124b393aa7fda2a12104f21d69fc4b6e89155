"""
Channel service for managing forced subscription channels
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from src.database import Database
from src.models.channel import Channel
from src.utils.logger import log_admin_action
from config import Config

logger = logging.getLogger(__name__)

class ChannelService:
    """Service for channel management operations"""
    
    def __init__(self, database: Database):
        self.db = database
        self.bot: Optional[Bot] = None
    
    def set_bot(self, bot: Bot):
        """Set bot instance for channel operations"""
        self.bot = bot
    
    async def add_channel(self, channel_id: str, channel_name: str, admin_id: int,
                         channel_username: str = None, join_link: str = None) -> Optional[Channel]:
        """Add a new required channel"""
        try:
            # Check if channel already exists
            existing = await self.db.channels.find_one({'channel_id': channel_id})
            if existing:
                return Channel.from_dict(existing)
            
            # Create channel
            channel = Channel(
                channel_id=channel_id,
                channel_name=channel_name,
                channel_username=channel_username,
                join_link=join_link,
                added_by=admin_id
            )
            
            # Check bot permissions if bot is available
            if self.bot:
                await self._check_bot_permissions(channel)
            
            # Save to database
            await self.db.channels.insert_one(channel.to_dict())
            
            log_admin_action(admin_id, "CHANNEL_ADDED", channel_id, f"Name: {channel_name}")
            logger.info(f"Added channel {channel_id} by admin {admin_id}")
            
            return channel
            
        except Exception as e:
            logger.error(f"Failed to add channel {channel_id}: {e}")
            return None
    
    async def get_channel(self, channel_id: str) -> Optional[Channel]:
        """Get channel by ID"""
        try:
            channel_data = await self.db.channels.find_one({'channel_id': channel_id})
            if channel_data:
                return Channel.from_dict(channel_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get channel {channel_id}: {e}")
            return None
    
    async def update_channel(self, channel: Channel) -> bool:
        """Update channel in database"""
        try:
            channel.updated_at = datetime.now(timezone.utc)
            result = await self.db.channels.update_one(
                {'channel_id': channel.channel_id},
                {'$set': channel.to_dict()}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Failed to update channel {channel.channel_id}: {e}")
            return False
    
    async def remove_channel(self, channel_id: str, admin_id: int) -> bool:
        """Remove a channel"""
        try:
            result = await self.db.channels.delete_one({'channel_id': channel_id})
            
            if result.deleted_count > 0:
                log_admin_action(admin_id, "CHANNEL_REMOVED", channel_id)
                logger.info(f"Removed channel {channel_id} by admin {admin_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to remove channel {channel_id}: {e}")
            return False
    
    async def get_all_channels(self) -> List[Channel]:
        """Get all channels"""
        try:
            cursor = self.db.channels.find().sort('created_at', -1)
            channels = []
            
            async for channel_data in cursor:
                channels.append(Channel.from_dict(channel_data))
            
            return channels
            
        except Exception as e:
            logger.error(f"Failed to get all channels: {e}")
            return []
    
    async def get_required_channels(self) -> List[Channel]:
        """Get all active required channels"""
        try:
            cursor = self.db.channels.find({
                'is_active': True,
                'is_required': True
            }).sort('created_at', -1)
            
            channels = []
            async for channel_data in cursor:
                channels.append(Channel.from_dict(channel_data))
            
            return channels
            
        except Exception as e:
            logger.error(f"Failed to get required channels: {e}")
            return []
    
    async def activate_channel(self, channel_id: str, admin_id: int) -> bool:
        """Activate a channel"""
        try:
            channel = await self.get_channel(channel_id)
            if not channel:
                return False
            
            channel.activate_channel()
            result = await self.update_channel(channel)
            
            if result:
                log_admin_action(admin_id, "CHANNEL_ACTIVATED", channel_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to activate channel {channel_id}: {e}")
            return False
    
    async def deactivate_channel(self, channel_id: str, admin_id: int) -> bool:
        """Deactivate a channel"""
        try:
            channel = await self.get_channel(channel_id)
            if not channel:
                return False
            
            channel.deactivate_channel()
            result = await self.update_channel(channel)
            
            if result:
                log_admin_action(admin_id, "CHANNEL_DEACTIVATED", channel_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to deactivate channel {channel_id}: {e}")
            return False
    
    async def is_user_member(self, user_id: int, channel_id: str) -> bool:
        """Check if user is a member of the channel"""
        try:
            if self.bot is None:
                return True  # Assume true if bot not available
            
            try:
                member = await self.bot.get_chat_member(chat_id=channel_id, user_id=user_id)
                return member.status in ['member', 'administrator', 'creator']
            except TelegramError as e:
                if "user not found" in str(e).lower():
                    return False
                # If we can't check, assume they're not a member
                logger.warning(f"Could not check membership for user {user_id} in channel {channel_id}: {e}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to check membership for user {user_id} in channel {channel_id}: {e}")
            return False
    
    async def verify_user_subscriptions(self, user_id: int) -> Dict[str, Any]:
        """Verify user subscriptions to all required channels"""
        try:
            required_channels = await self.get_required_channels()
            
            if not required_channels:
                return {'all_joined': True, 'missing_channels': []}
            
            missing_channels = []
            
            for channel in required_channels:
                is_member = await self.is_user_member(user_id, channel.channel_id)
                if not is_member:
                    missing_channels.append({
                        'channel_id': channel.channel_id,
                        'channel_name': channel.channel_name,
                        'join_link': channel.get_join_link()
                    })
                else:
                    # Update channel statistics
                    channel.increment_verifications()
                    await self.update_channel(channel)
            
            return {
                'all_joined': len(missing_channels) == 0,
                'missing_channels': missing_channels,
                'total_required': len(required_channels),
                'joined_count': len(required_channels) - len(missing_channels)
            }
            
        except Exception as e:
            logger.error(f"Failed to verify subscriptions for user {user_id}: {e}")
            return {'all_joined': False, 'missing_channels': []}
    
    async def _check_bot_permissions(self, channel: Channel):
        """Check bot permissions in the channel"""
        try:
            if not self.bot:
                return
            
            try:
                bot_member = await self.bot.get_chat_member(
                    chat_id=channel.channel_id,
                    user_id=self.bot.id
                )
                
                is_admin = bot_member.status in ['administrator', 'creator']
                can_invite = getattr(bot_member, 'can_invite_users', False)
                
                channel.update_bot_permissions(is_admin, can_invite)
                
            except TelegramError as e:
                logger.warning(f"Could not check bot permissions in channel {channel.channel_id}: {e}")
                channel.update_bot_permissions(False, False)
                
        except Exception as e:
            logger.error(f"Failed to check bot permissions for channel {channel.channel_id}: {e}")
    
    async def update_channel_info(self, channel_id: str) -> bool:
        """Update channel information from Telegram"""
        try:
            if not self.bot:
                return False
            
            channel = await self.get_channel(channel_id)
            if not channel:
                return False
            
            try:
                chat = await self.bot.get_chat(chat_id=channel_id)
                
                # Update channel info
                channel.channel_name = chat.title or channel.channel_name
                channel.channel_username = chat.username
                channel.subscriber_count = getattr(chat, 'member_count', None)
                
                # Check bot permissions
                await self._check_bot_permissions(channel)
                
                return await self.update_channel(channel)
                
            except TelegramError as e:
                logger.warning(f"Could not update info for channel {channel_id}: {e}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to update channel info for {channel_id}: {e}")
            return False
    
    async def get_channel_statistics(self) -> Dict[str, Any]:
        """Get channel statistics"""
        try:
            total_channels = await self.db.channels.count_documents({})
            active_channels = await self.db.channels.count_documents({'is_active': True})
            required_channels = await self.db.channels.count_documents({
                'is_active': True,
                'is_required': True
            })
            
            # Get total joins and verifications
            pipeline = [
                {'$group': {
                    '_id': None,
                    'total_joins': {'$sum': '$total_joins'},
                    'total_verifications': {'$sum': '$successful_verifications'}
                }}
            ]
            
            result = await self.db.channels.aggregate(pipeline).to_list(1)
            total_joins = result[0]['total_joins'] if result else 0
            total_verifications = result[0]['total_verifications'] if result else 0
            
            return {
                'total_channels': total_channels,
                'active_channels': active_channels,
                'required_channels': required_channels,
                'total_joins': total_joins,
                'total_verifications': total_verifications,
                'verification_rate': (total_verifications / total_joins * 100) if total_joins > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get channel statistics: {e}")
            return {}
    
    async def auto_detect_channel_from_message(self, forwarded_message) -> Optional[Dict[str, str]]:
        """Auto-detect channel information from forwarded message"""
        try:
            if not forwarded_message or not forwarded_message.forward_from_chat:
                return None
            
            chat = forwarded_message.forward_from_chat
            
            if chat.type not in ['channel', 'supergroup']:
                return None
            
            return {
                'channel_id': str(chat.id),
                'channel_name': chat.title,
                'channel_username': chat.username,
                'join_link': f"https://t.me/{chat.username}" if chat.username else None
            }
            
        except Exception as e:
            logger.error(f"Failed to auto-detect channel from message: {e}")
            return None
