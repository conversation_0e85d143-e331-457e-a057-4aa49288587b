from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional

@dataclass
class AdminSettings:
    """Admin configurable settings for the bot"""

    # Financial Settings - Use Config values as defaults (integer currency)
    referral_reward: int = field(default_factory=lambda: __import__('config').Config.REFERRAL_REWARD)
    daily_bonus_amount: str = field(default_factory=lambda: str(__import__('config').Config.DAILY_BONUS_AMOUNT))
    minimum_withdrawal: int = field(default_factory=lambda: __import__('config').Config.MINIMUM_WITHDRAWAL)
    withdrawal_cooldown_hours: int = field(default_factory=lambda: __import__('config').Config.WITHDRAWAL_COOLDOWN_HOURS)

    # Task Settings (integer currency)
    task_rewards: Dict[str, int] = field(default_factory=lambda: {
        "join_channel": 25,
        "share_bot": 50,
        "daily_streak": 100,
        "referral_milestone": 150
    })

    # Bot Settings (integer currency)
    welcome_bonus: int = 0
    max_daily_claims: int = 1
    referral_limit_per_day: int = 10
    
    # Admin Info
    updated_by: Optional[int] = None
    updated_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    @classmethod
    def from_dict(cls, data: dict) -> 'AdminSettings':
        """Create AdminSettings from dictionary"""
        # Remove MongoDB _id field if present
        data = data.copy()
        data.pop('_id', None)

        # Remove any unknown fields that might cause initialization errors
        valid_fields = {
            'referral_reward', 'daily_bonus_amount', 'minimum_withdrawal', 'withdrawal_cooldown_hours',
            'task_rewards', 'welcome_bonus', 'max_daily_claims', 'referral_limit_per_day',
            'updated_by', 'updated_at', 'created_at'
        }

        # Filter out any fields not in the AdminSettings dataclass
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}

        # Convert daily_bonus_amount to string if it's a number (backward compatibility)
        if 'daily_bonus_amount' in filtered_data:
            if isinstance(filtered_data['daily_bonus_amount'], (int, float)):
                filtered_data['daily_bonus_amount'] = str(int(filtered_data['daily_bonus_amount']))

        # Convert datetime strings back to datetime objects
        if 'created_at' in filtered_data and isinstance(filtered_data['created_at'], str):
            filtered_data['created_at'] = datetime.fromisoformat(filtered_data['created_at'])
        if 'updated_at' in filtered_data and isinstance(filtered_data['updated_at'], str):
            filtered_data['updated_at'] = datetime.fromisoformat(filtered_data['updated_at'])

        return cls(**filtered_data)
    
    def to_dict(self) -> dict:
        """Convert AdminSettings to dictionary for MongoDB storage"""
        data = {
            'referral_reward': self.referral_reward,
            'daily_bonus_amount': self.daily_bonus_amount,
            'minimum_withdrawal': self.minimum_withdrawal,
            'withdrawal_cooldown_hours': self.withdrawal_cooldown_hours,
            'task_rewards': self.task_rewards,
            'welcome_bonus': self.welcome_bonus,
            'max_daily_claims': self.max_daily_claims,
            'referral_limit_per_day': self.referral_limit_per_day,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
        return data
    
    def update_settings(self, admin_id: int, **kwargs):
        """Update settings with admin tracking"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        self.updated_by = admin_id
        self.updated_at = datetime.utcnow()
