"""
Configuration monitoring service for real-time .env file changes
"""

import asyncio
import os
import time
import logging
from pathlib import Path
from typing import Optional, Callable
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from config import Config

logger = logging.getLogger(__name__)

class ConfigFileHandler(FileSystemEventHandler):
    """Handler for .env file changes"""
    
    def __init__(self, callback: Callable):
        self.callback = callback
        self.last_modified = 0
        self.debounce_delay = 2  # seconds
    
    def on_modified(self, event):
        """Handle file modification events"""
        if event.is_directory:
            return
        
        if event.src_path.endswith('.env'):
            current_time = time.time()
            
            # Debounce rapid file changes
            if current_time - self.last_modified > self.debounce_delay:
                self.last_modified = current_time
                logger.info(f"📁 .env file modified: {event.src_path}")
                
                # Schedule callback with delay to ensure file write is complete
                asyncio.create_task(self._delayed_callback())
    
    async def _delayed_callback(self):
        """Execute callback with delay to ensure file write completion"""
        await asyncio.sleep(1)  # Wait for file write to complete
        await self.callback()

class ConfigMonitorService:
    """Service for monitoring and auto-reloading configuration changes"""
    
    def __init__(self, admin_settings_service=None):
        self.admin_settings_service = admin_settings_service
        self.observer: Optional[Observer] = None
        self.env_file_path = Path('.env')
        self.last_reload_time = 0
        self.reload_cooldown = 5  # seconds between reloads
        
    async def start_monitoring(self):
        """Start monitoring .env file for changes"""
        try:
            if not self.env_file_path.exists():
                logger.warning("⚠️ .env file not found. Configuration monitoring disabled.")
                return False
            
            # Set up file watcher
            event_handler = ConfigFileHandler(self._handle_config_change)
            self.observer = Observer()
            self.observer.schedule(event_handler, str(self.env_file_path.parent), recursive=False)
            self.observer.start()
            
            logger.info(f"🔍 Configuration monitoring started for: {self.env_file_path.absolute()}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start configuration monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop monitoring .env file"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            logger.info("🛑 Configuration monitoring stopped")
    
    async def _handle_config_change(self):
        """Handle configuration file changes"""
        try:
            current_time = time.time()
            
            # Prevent rapid reloads
            if current_time - self.last_reload_time < self.reload_cooldown:
                logger.debug("⏳ Skipping reload due to cooldown")
                return
            
            self.last_reload_time = current_time
            
            logger.info("🔄 .env file changed, reloading configuration...")
            
            # Store old values for comparison
            old_values = {
                'MINIMUM_WITHDRAWAL': Config.MINIMUM_WITHDRAWAL,
                'REFERRAL_REWARD': Config.REFERRAL_REWARD,
                'DAILY_BONUS_AMOUNT': Config.DAILY_BONUS_AMOUNT,
                'WITHDRAWAL_COOLDOWN_HOURS': Config.WITHDRAWAL_COOLDOWN_HOURS
            }
            
            # Reload configuration
            success = Config.reload_config()
            
            if success:
                # Check for changes and log them
                changes = []
                
                if old_values['MINIMUM_WITHDRAWAL'] != Config.MINIMUM_WITHDRAWAL:
                    changes.append(f"Minimum Withdrawal: 💎{old_values['MINIMUM_WITHDRAWAL']} → 💎{Config.MINIMUM_WITHDRAWAL}")
                
                if old_values['REFERRAL_REWARD'] != Config.REFERRAL_REWARD:
                    changes.append(f"Referral Reward: 💎{old_values['REFERRAL_REWARD']} → 💎{Config.REFERRAL_REWARD}")
                
                if old_values['DAILY_BONUS_AMOUNT'] != Config.DAILY_BONUS_AMOUNT:
                    changes.append(f"Daily Bonus: 💎{old_values['DAILY_BONUS_AMOUNT']} → 💎{Config.DAILY_BONUS_AMOUNT}")
                
                if old_values['WITHDRAWAL_COOLDOWN_HOURS'] != Config.WITHDRAWAL_COOLDOWN_HOURS:
                    changes.append(f"Withdrawal Cooldown: {old_values['WITHDRAWAL_COOLDOWN_HOURS']}h → {Config.WITHDRAWAL_COOLDOWN_HOURS}h")
                
                if changes:
                    logger.info("📊 Configuration changes detected:")
                    for change in changes:
                        logger.info(f"   • {change}")
                    
                    # Sync with admin settings if available
                    if self.admin_settings_service:
                        await self._sync_admin_settings()
                else:
                    logger.info("ℹ️ Configuration reloaded, no changes detected")
                
            else:
                logger.error("❌ Configuration reload failed")
                
        except Exception as e:
            logger.error(f"❌ Error handling configuration change: {e}")
    
    async def _sync_admin_settings(self):
        """Synchronize admin settings with new Config values"""
        try:
            logger.info("🔄 Synchronizing admin settings with new Config values...")
            
            # Get current admin settings
            settings = await self.admin_settings_service.get_settings()
            
            # Update admin settings to match Config values
            updated = False
            
            if settings.minimum_withdrawal != Config.MINIMUM_WITHDRAWAL:
                settings.minimum_withdrawal = Config.MINIMUM_WITHDRAWAL
                updated = True
            
            if settings.referral_reward != Config.REFERRAL_REWARD:
                settings.referral_reward = Config.REFERRAL_REWARD
                updated = True
            
            if settings.withdrawal_cooldown_hours != Config.WITHDRAWAL_COOLDOWN_HOURS:
                settings.withdrawal_cooldown_hours = Config.WITHDRAWAL_COOLDOWN_HOURS
                updated = True
            
            if str(settings.daily_bonus_amount) != str(Config.DAILY_BONUS_AMOUNT):
                settings.daily_bonus_amount = str(Config.DAILY_BONUS_AMOUNT)
                updated = True
            
            if updated:
                await self.admin_settings_service.save_settings(settings)
                logger.info("✅ Admin settings synchronized with Config values")
            else:
                logger.info("ℹ️ Admin settings already in sync")
                
        except Exception as e:
            logger.error(f"❌ Failed to sync admin settings: {e}")
    
    async def manual_reload(self) -> bool:
        """Manually trigger configuration reload"""
        try:
            logger.info("🔄 Manual configuration reload triggered...")
            await self._handle_config_change()
            return True
        except Exception as e:
            logger.error(f"❌ Manual reload failed: {e}")
            return False
    
    def get_env_file_info(self) -> dict:
        """Get information about the .env file"""
        try:
            if self.env_file_path.exists():
                stat = self.env_file_path.stat()
                return {
                    'exists': True,
                    'path': str(self.env_file_path.absolute()),
                    'size': stat.st_size,
                    'modified': time.ctime(stat.st_mtime),
                    'monitoring': self.observer is not None and self.observer.is_alive()
                }
            else:
                return {
                    'exists': False,
                    'path': str(self.env_file_path.absolute()),
                    'monitoring': False
                }
        except Exception as e:
            logger.error(f"Failed to get .env file info: {e}")
            return {'exists': False, 'error': str(e)}
