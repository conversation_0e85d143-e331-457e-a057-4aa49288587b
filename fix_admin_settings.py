#!/usr/bin/env python3
"""
Script to fix admin settings in database
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config import Config
from database import Database
from services.admin_settings_service import AdminSettingsService

async def fix_admin_settings():
    """Fix admin settings in database"""
    
    print("🔧 Fixing Admin Settings in Database")
    print("=" * 50)
    
    try:
        # Initialize database and services
        db = Database()
        await db.connect()
        
        admin_settings_service = AdminSettingsService(db.db)
        
        print("📋 Current Database Values:")
        print("-" * 30)
        
        # Get current settings from database
        current_settings = await admin_settings_service.get_settings()
        print(f"Daily Bonus: {current_settings.daily_bonus_amount} (type: {type(current_settings.daily_bonus_amount)})")
        print(f"Minimum Withdrawal: {current_settings.minimum_withdrawal}")
        print(f"Referral Reward: {current_settings.referral_reward}")
        print(f"Updated By: {current_settings.updated_by}")
        print(f"Updated At: {current_settings.updated_at}")
        
        print("\n📋 Current Config Values:")
        print("-" * 30)
        print(f"Config DAILY_BONUS_AMOUNT: {Config.DAILY_BONUS_AMOUNT}")
        print(f"Config MINIMUM_WITHDRAWAL: {Config.MINIMUM_WITHDRAWAL}")
        print(f"Config REFERRAL_REWARD: {Config.REFERRAL_REWARD}")
        
        print("\n🔄 Updating Database to Match Config...")
        print("-" * 30)
        
        # Update settings to match current config
        admin_id = 999999999  # System update ID
        
        # Update daily bonus amount (ensure it's a string)
        success1 = await admin_settings_service.update_setting(
            'daily_bonus_amount', 
            str(Config.DAILY_BONUS_AMOUNT), 
            admin_id
        )
        print(f"Daily Bonus Update: {'✅' if success1 else '❌'}")
        
        # Update minimum withdrawal
        success2 = await admin_settings_service.update_setting(
            'minimum_withdrawal', 
            Config.MINIMUM_WITHDRAWAL, 
            admin_id
        )
        print(f"Minimum Withdrawal Update: {'✅' if success2 else '❌'}")
        
        # Update referral reward
        success3 = await admin_settings_service.update_setting(
            'referral_reward', 
            Config.REFERRAL_REWARD, 
            admin_id
        )
        print(f"Referral Reward Update: {'✅' if success3 else '❌'}")
        
        print("\n📊 Updated Database Values:")
        print("-" * 30)
        
        # Get updated settings
        updated_settings = await admin_settings_service.get_settings()
        print(f"Daily Bonus: {updated_settings.daily_bonus_amount} (type: {type(updated_settings.daily_bonus_amount)})")
        print(f"Minimum Withdrawal: {updated_settings.minimum_withdrawal}")
        print(f"Referral Reward: {updated_settings.referral_reward}")
        print(f"Updated By: {updated_settings.updated_by}")
        print(f"Updated At: {updated_settings.updated_at}")
        
        # Test admin panel functionality
        print("\n🧪 Testing Admin Panel Updates...")
        print("-" * 30)
        
        # Test updating daily bonus through admin method
        test_success = await admin_settings_service.update_setting(
            'daily_bonus_amount', 
            '8-20',  # Test range format
            123456789  # Test admin ID
        )
        print(f"Test Range Update: {'✅' if test_success else '❌'}")
        
        if test_success:
            test_settings = await admin_settings_service.get_settings()
            print(f"Test Daily Bonus: {test_settings.daily_bonus_amount}")
            print(f"Test Updated By: {test_settings.updated_by}")
        
        # Restore original value
        await admin_settings_service.update_setting(
            'daily_bonus_amount', 
            str(Config.DAILY_BONUS_AMOUNT), 
            admin_id
        )
        
        await db.close()
        
        print("\n✅ Database Fix Complete!")
        print("Now try using the admin panel: /admin → Bot Settings → Daily Bonus")
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()

async def show_database_query():
    """Show how to check the database manually"""
    
    print("\n🔍 Manual Database Check")
    print("=" * 50)
    
    print("**MongoDB Atlas/Compass Query:**")
    print("Collection: admin_settings")
    print("Query: {} (empty - shows all documents)")
    print()
    
    print("**MongoDB Shell Commands:**")
    print("```javascript")
    print("// Connect to your database")
    print("use referral_bot_db")
    print()
    print("// View current admin settings")
    print("db.admin_settings.find().pretty()")
    print()
    print("// View only daily bonus")
    print("db.admin_settings.find({}, {daily_bonus_amount: 1, updated_at: 1})")
    print()
    print("// Manual update if needed")
    print("db.admin_settings.updateOne(")
    print("  {},")
    print("  {")
    print("    $set: {")
    print(f'      "daily_bonus_amount": "{Config.DAILY_BONUS_AMOUNT}",')
    print(f'      "minimum_withdrawal": {Config.MINIMUM_WITHDRAWAL},')
    print('      "updated_at": new Date().toISOString()')
    print("    }")
    print("  }")
    print(")")
    print("```")

def show_admin_panel_path():
    """Show how to access admin panel"""
    
    print("\n📱 Admin Panel Access")
    print("=" * 50)
    
    print("**Step-by-Step:**")
    print("1. Send /admin to your bot")
    print("2. Click '⚙️ Bot Settings'")
    print("3. Click '🎁 Daily Bonus'")
    print("4. Enter new value:")
    print("   • Single: '15' → gives exactly 💎15")
    print("   • Range: '8-20' → gives random 💎8-💎20")
    print("5. Value saved to database instantly")
    print()
    
    print("**Current Config Values:**")
    print(f"• Daily Bonus: {Config.DAILY_BONUS_AMOUNT}")
    print(f"• Min Withdrawal: {Config.MINIMUM_WITHDRAWAL}")
    print(f"• Referral Reward: {Config.REFERRAL_REWARD}")
    print()
    
    print("**After Fix, Database Should Show:**")
    print("• daily_bonus_amount: string format")
    print("• updated_by: admin user ID")
    print("• updated_at: timestamp")

async def test_admin_panel_flow():
    """Test the complete admin panel flow"""

    print("\n🧪 Testing Admin Panel Flow")
    print("=" * 50)

    try:
        # Initialize database and services
        db = Database()
        await db.connect()

        admin_settings_service = AdminSettingsService(db.db)

        print("📋 Testing Setting Update Flow:")
        print("-" * 30)

        # Test the exact flow that happens in admin panel
        test_admin_id = 123456789

        # 1. Get current settings (like admin panel does)
        current_settings = await admin_settings_service.get_settings()
        print(f"1. Current daily bonus: {current_settings.daily_bonus_amount}")
        print(f"1. Current referral reward: {current_settings.referral_reward}")

        # 2. Update setting (like _process_admin_setting_change does)
        test_value = "10-20"
        success = await admin_settings_service.update_setting('daily_bonus_amount', test_value, test_admin_id)
        print(f"2. Daily bonus update success: {'✅' if success else '❌'}")

        # Test referral reward update
        test_referral_reward = 15
        success2 = await admin_settings_service.update_setting('referral_reward', test_referral_reward, test_admin_id)
        print(f"2. Referral reward update success: {'✅' if success2 else '❌'}")

        # 3. Verify the updates
        if success and success2:
            updated_settings = await admin_settings_service.get_settings()
            print(f"3. New daily bonus: {updated_settings.daily_bonus_amount}")
            print(f"3. New referral reward: {updated_settings.referral_reward}")
            print(f"4. Updated by: {updated_settings.updated_by}")
            print(f"5. Updated at: {updated_settings.updated_at}")

            if updated_settings.daily_bonus_amount == test_value and updated_settings.referral_reward == test_referral_reward:
                print("✅ Admin panel flow test PASSED")
            else:
                print("❌ Admin panel flow test FAILED - values not saved correctly")
        else:
            print("❌ Admin panel flow test FAILED - updates failed")

        # Restore original values
        await admin_settings_service.update_setting(
            'daily_bonus_amount',
            str(Config.DAILY_BONUS_AMOUNT),
            999999999
        )
        await admin_settings_service.update_setting(
            'referral_reward',
            Config.REFERRAL_REWARD,
            999999999
        )

        await db.close()

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_referral_service_integration():
    """Test that ReferralService uses dynamic settings"""

    print("\n🔗 Testing Referral Service Integration")
    print("=" * 50)

    try:
        # Initialize database and services
        db = Database()
        await db.connect()

        admin_settings_service = AdminSettingsService(db.db)

        # Test ReferralService with admin settings
        from services.referral_service import ReferralService
        referral_service = ReferralService(db, admin_settings_service)

        print("📋 Testing Referral Service Dynamic Settings:")
        print("-" * 30)

        # 1. Set a test referral reward
        test_reward = 25
        await admin_settings_service.update_setting('referral_reward', test_reward, 123456789)
        print(f"1. Set referral reward to: 💎{test_reward}")

        # 2. Test creating a referral (this should use the dynamic setting)
        test_referrer_id = 999999999
        test_referred_id = 888888888
        test_code = "TEST123"

        # Clean up any existing test referrals
        await db.db.referrals.delete_many({
            'referrer_id': test_referrer_id,
            'referred_id': test_referred_id
        })

        # Create referral
        referral = await referral_service.create_referral(test_referrer_id, test_referred_id, test_code)

        if referral:
            print(f"2. Created referral with reward: 💎{referral.reward_amount}")
            if referral.reward_amount == test_reward:
                print("✅ ReferralService uses dynamic settings correctly")
            else:
                print(f"❌ ReferralService still uses hardcoded value (expected {test_reward}, got {referral.reward_amount})")
        else:
            print("❌ Failed to create referral")

        # Clean up test data
        await db.db.referrals.delete_many({
            'referrer_id': test_referrer_id,
            'referred_id': test_referred_id
        })

        # Restore original referral reward
        await admin_settings_service.update_setting('referral_reward', Config.REFERRAL_REWARD, 999999999)

        await db.close()

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Admin Settings Database Fix & Referral Integration Test")
    print("This script will fix admin panel settings and test referral reward integration")
    print()

    asyncio.run(fix_admin_settings())
    asyncio.run(test_admin_panel_flow())
    asyncio.run(test_referral_service_integration())
    asyncio.run(show_database_query())
    show_admin_panel_path()

    print("\n🎯 Next Steps:")
    print("1. Check your database again")
    print("2. Try the admin panel: /admin → Bot Settings → Referral Reward")
    print("3. Change referral reward from 💎10 to 💎15")
    print("4. Test with a new referral - should award 💎15 (not 💎10)")
    print("5. Check referral stats - should show 'Reward per Friend: 15💎'")
    print("\n🔧 If referral rewards still show old amounts:")
    print("- Restart the bot after running this fix")
    print("- Check bot logs for 'Processing message from user' and 'admin_setting_change'")
    print("- Ensure you're an admin (user ID in Config.ADMIN_USER_IDS)")
    print("- Verify database shows updated referral_reward value")
    print("\n✅ Expected Behavior After Fix:")
    print("- Admin panel updates should immediately affect new referrals")
    print("- Referral notifications should show updated reward amounts")
    print("- Referral stats should display current admin-set reward values")
