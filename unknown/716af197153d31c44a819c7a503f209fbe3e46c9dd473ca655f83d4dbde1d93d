"""
Channel model for the Telegram Referral Bot
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
import uuid

@dataclass
class Channel:
    """Channel model representing required subscription channels"""
    
    channel_id: str  # Telegram channel ID (with or without @)
    channel_name: str
    channel_username: Optional[str] = None
    
    # Channel settings
    is_active: bool = True
    is_required: bool = True
    join_link: Optional[str] = None
    
    # Bot permissions
    bot_is_admin: bool = False
    can_invite_users: bool = False
    
    # Optional fields
    id: Optional[str] = None
    description: Optional[str] = None
    subscriber_count: Optional[int] = None
    
    # Admin fields
    added_by: Optional[int] = None  # Admin user ID who added the channel
    
    # Timestamps
    created_at: datetime = None
    updated_at: datetime = None
    last_checked: Optional[datetime] = None
    
    # Statistics
    total_joins: int = 0
    successful_verifications: int = 0
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.id is None:
            self.id = str(uuid.uuid4())
        
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        
        if self.updated_at is None:
            self.updated_at = datetime.now(timezone.utc)
        
        if self.metadata is None:
            self.metadata = {}
        
        # Normalize channel ID
        if self.channel_id and not self.channel_id.startswith('@'):
            if not self.channel_id.startswith('-'):
                self.channel_id = f"@{self.channel_id}"
    
    def update_bot_permissions(self, is_admin: bool, can_invite: bool = False):
        """Update bot permissions in the channel"""
        self.bot_is_admin = is_admin
        self.can_invite_users = can_invite
        self.last_checked = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def update_subscriber_count(self, count: int):
        """Update subscriber count"""
        self.subscriber_count = count
        self.last_checked = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def increment_joins(self):
        """Increment join count"""
        self.total_joins += 1
        self.updated_at = datetime.now(timezone.utc)
    
    def increment_verifications(self):
        """Increment successful verification count"""
        self.successful_verifications += 1
        self.updated_at = datetime.now(timezone.utc)
    
    def activate_channel(self):
        """Activate the channel"""
        self.is_active = True
        self.updated_at = datetime.now(timezone.utc)
    
    def deactivate_channel(self):
        """Deactivate the channel"""
        self.is_active = False
        self.updated_at = datetime.now(timezone.utc)
    
    def set_required(self, required: bool = True):
        """Set channel as required or optional"""
        self.is_required = required
        self.updated_at = datetime.now(timezone.utc)
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata to channel"""
        self.metadata[key] = value
        self.updated_at = datetime.now(timezone.utc)
    
    def get_display_name(self) -> str:
        """Get channel display name"""
        if self.channel_username:
            return f"@{self.channel_username}"
        return self.channel_name
    
    def get_join_link(self) -> str:
        """Get channel join link"""
        if self.join_link:
            return self.join_link
        
        if self.channel_username:
            return f"https://t.me/{self.channel_username}"
        
        return f"https://t.me/{self.channel_id.replace('@', '')}"
    
    def get_status_display(self) -> str:
        """Get channel status display"""
        status = "🟢 Active" if self.is_active else "🔴 Inactive"
        required = " (Required)" if self.is_required else " (Optional)"
        admin = " 👑" if self.bot_is_admin else ""
        
        return f"{status}{required}{admin}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert channel object to dictionary"""
        data = asdict(self)
        
        # Convert datetime objects to ISO format
        datetime_fields = ['created_at', 'updated_at', 'last_checked']
        for field in datetime_fields:
            if field in data and data[field]:
                data[field] = data[field].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Channel':
        """Create channel object from dictionary"""
        # Create a copy of data to avoid modifying the original
        channel_data = data.copy()

        # Remove MongoDB's _id field if present
        channel_data.pop('_id', None)

        # Convert ISO format strings back to datetime objects
        datetime_fields = ['created_at', 'updated_at', 'last_checked']
        for field in datetime_fields:
            if field in channel_data and channel_data[field]:
                if isinstance(channel_data[field], str):
                    channel_data[field] = datetime.fromisoformat(channel_data[field].replace('Z', '+00:00'))

        return cls(**channel_data)
    
    def __str__(self) -> str:
        """String representation of channel"""
        return f"Channel(id={self.channel_id}, name={self.channel_name}, active={self.is_active})"
