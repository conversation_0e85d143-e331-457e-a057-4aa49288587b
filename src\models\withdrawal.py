"""
Withdrawal model for the Telegram Referral Bot
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

class WithdrawalStatus(Enum):
    """Withdrawal status"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class WithdrawalType(Enum):
    """Withdrawal type"""
    CASH = "cash"
    PRODUCT = "product"

@dataclass
class Withdrawal:
    """Withdrawal model representing user withdrawal requests"""
    
    user_id: int
    amount: float
    withdrawal_type: WithdrawalType
    status: WithdrawalStatus = WithdrawalStatus.PENDING
    
    # Product-specific fields (for digital products)
    product_id: Optional[str] = None
    product_name: Optional[str] = None
    
    # Payment/delivery details
    payment_method: Optional[str] = None
    payment_details: Optional[Dict[str, Any]] = None
    delivery_info: Optional[str] = None
    
    # Optional fields
    withdrawal_id: Optional[str] = None
    transaction_id: Optional[str] = None
    
    # Admin fields
    processed_by: Optional[int] = None  # Admin user ID
    admin_notes: Optional[str] = None
    rejection_reason: Optional[str] = None
    
    # Timestamps
    created_at: datetime = None
    updated_at: datetime = None
    processed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.withdrawal_id is None:
            self.withdrawal_id = str(uuid.uuid4())
        
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        
        if self.updated_at is None:
            self.updated_at = datetime.now(timezone.utc)
        
        if self.payment_details is None:
            self.payment_details = {}
        
        if self.metadata is None:
            self.metadata = {}
    
    def approve_withdrawal(self, admin_id: int, notes: Optional[str] = None):
        """Approve withdrawal request"""
        self.status = WithdrawalStatus.APPROVED
        self.processed_by = admin_id
        self.processed_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
        
        if notes:
            self.admin_notes = notes
    
    def reject_withdrawal(self, admin_id: int, reason: str):
        """Reject withdrawal request"""
        self.status = WithdrawalStatus.REJECTED
        self.processed_by = admin_id
        self.rejection_reason = reason
        self.processed_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
    
    def complete_withdrawal(self, admin_id: Optional[int] = None, delivery_info: Optional[str] = None):
        """Mark withdrawal as completed"""
        self.status = WithdrawalStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
        self.updated_at = datetime.now(timezone.utc)
        
        if admin_id:
            self.processed_by = admin_id
        
        if delivery_info:
            self.delivery_info = delivery_info
    
    def cancel_withdrawal(self, reason: str = ""):
        """Cancel withdrawal request"""
        self.status = WithdrawalStatus.CANCELLED
        self.updated_at = datetime.now(timezone.utc)
        
        if reason:
            self.admin_notes = reason
    
    def add_payment_detail(self, key: str, value: Any):
        """Add payment detail"""
        self.payment_details[key] = value
        self.updated_at = datetime.now(timezone.utc)
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata to withdrawal"""
        self.metadata[key] = value
        self.updated_at = datetime.now(timezone.utc)
    
    def get_status_display(self) -> str:
        """Get human-readable status"""
        status_map = {
            WithdrawalStatus.PENDING: "⏳ Pending",
            WithdrawalStatus.APPROVED: "✅ Approved",
            WithdrawalStatus.REJECTED: "❌ Rejected",
            WithdrawalStatus.COMPLETED: "🎉 Completed",
            WithdrawalStatus.CANCELLED: "🚫 Cancelled"
        }
        return status_map.get(self.status, str(self.status.value))
    
    def get_type_display(self) -> str:
        """Get human-readable withdrawal type"""
        type_map = {
            WithdrawalType.CASH: "💰 Cash",
            WithdrawalType.PRODUCT: "🎁 Product"
        }
        return type_map.get(self.withdrawal_type, str(self.withdrawal_type.value))
    
    def get_formatted_amount(self, currency_symbol: str = "💎") -> str:
        """Get formatted amount with currency symbol"""
        return f"{currency_symbol}{self.amount:.2f}"
    
    def is_pending(self) -> bool:
        """Check if withdrawal is pending"""
        return self.status == WithdrawalStatus.PENDING
    
    def is_approved(self) -> bool:
        """Check if withdrawal is approved"""
        return self.status == WithdrawalStatus.APPROVED
    
    def is_completed(self) -> bool:
        """Check if withdrawal is completed"""
        return self.status == WithdrawalStatus.COMPLETED
    
    def is_rejected(self) -> bool:
        """Check if withdrawal is rejected"""
        return self.status == WithdrawalStatus.REJECTED
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert withdrawal object to dictionary"""
        data = asdict(self)
        
        # Convert enum values to strings
        data['status'] = self.status.value
        data['withdrawal_type'] = self.withdrawal_type.value
        
        # Convert datetime objects to ISO format
        datetime_fields = ['created_at', 'updated_at', 'processed_at', 'completed_at']
        for field in datetime_fields:
            if field in data and data[field]:
                data[field] = data[field].isoformat()
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Withdrawal':
        """Create withdrawal object from dictionary"""
        # Create a copy of data to avoid modifying the original
        withdrawal_data = data.copy()

        # Remove MongoDB's _id field if present
        withdrawal_data.pop('_id', None)

        # Convert string values back to enums
        if 'status' in withdrawal_data:
            withdrawal_data['status'] = WithdrawalStatus(withdrawal_data['status'])

        if 'withdrawal_type' in withdrawal_data:
            withdrawal_data['withdrawal_type'] = WithdrawalType(withdrawal_data['withdrawal_type'])

        # Convert ISO format strings back to datetime objects
        datetime_fields = ['created_at', 'updated_at', 'processed_at', 'completed_at']
        for field in datetime_fields:
            if field in withdrawal_data and withdrawal_data[field]:
                if isinstance(withdrawal_data[field], str):
                    withdrawal_data[field] = datetime.fromisoformat(withdrawal_data[field].replace('Z', '+00:00'))

        return cls(**withdrawal_data)
    
    def __str__(self) -> str:
        """String representation of withdrawal"""
        return f"Withdrawal(id={self.withdrawal_id}, user={self.user_id}, amount={self.amount}, status={self.status.value})"
