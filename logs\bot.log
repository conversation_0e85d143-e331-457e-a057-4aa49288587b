2025-07-11 16:33:42 - __main__ - INFO - main:6100 - 👋 <PERSON><PERSON> stopped by user
2025-07-11 16:33:54 - __main__ - INFO - main:6049 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:33:54 - __main__ - INFO - main:6050 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:33:54 - __main__ - INFO - main:6054 - ✅ Configuration validated
2025-07-11 16:33:55 - __main__ - INFO - main:6078 - ✅ All handlers added successfully
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6083 - 🚀 Starting bot in long polling mode...
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6084 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6085 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6086 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 16:36:00 - __main__ - ERROR - _show_task_list:4518 - Error showing task list: Can't parse entities: can't find end of the entity starting at byte offset 132
2025-07-11 16:40:59 - __main__ - INFO - _show_task_creation_summary:4691 - Task creation summary data: {'step': 'reward', 'button_name': 'BOT OPENING', 'task_type': 'submit_image', 'reference_image_url': 'AgACAgUAAxkBAAIChmhw6ZD770sTm2WG4pNHM98jOYeQAAL6xjEbPh2JV066fQVu-U95AQADAgADeAADNgQ', 'instructions': 'HID\nfdsfAS', 'reward': 100.0}
2025-07-11 16:41:20 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:41:23 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:50:06 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:56:37 - __main__ - INFO - main:6183 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:56:37 - __main__ - INFO - main:6184 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:56:37 - __main__ - INFO - main:6188 - ✅ Configuration validated
2025-07-11 16:56:38 - __main__ - INFO - main:6212 - ✅ All handlers added successfully
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6217 - 🚀 Starting bot in long polling mode...
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6218 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6219 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6220 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 16:56:53 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:56:58 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:56:59 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:04 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:05 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:11 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:15 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:18 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:22 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:25 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:30 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:36 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:39 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:47 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:51 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:58:03 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:58:08 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:01:13 - __main__ - INFO - main:6183 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 17:01:13 - __main__ - INFO - main:6184 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 17:01:13 - __main__ - INFO - main:6188 - ✅ Configuration validated
2025-07-11 17:01:14 - __main__ - INFO - main:6212 - ✅ All handlers added successfully
2025-07-11 17:01:14 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 17:01:14 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 17:01:14 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 17:01:28 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 17:01:28 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 17:01:28 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 17:01:28 - __main__ - INFO - init_and_run:6217 - 🚀 Starting bot in long polling mode...
2025-07-11 17:01:28 - __main__ - INFO - init_and_run:6218 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 17:01:28 - __main__ - INFO - init_and_run:6219 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 17:01:28 - __main__ - INFO - init_and_run:6220 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 17:02:21 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-11 17:02:21 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: member
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1001296547211: member
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1002414699235: member
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1002414699235: member
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:260 - ✅ User 1381431908 verified as member of all required channels
2025-07-11 17:07:17 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-11 17:07:17 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1001296547211: administrator
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1002414699235: creator
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:260 - ✅ User 8153676253 verified as member of all required channels
2025-07-11 17:21:13 - __main__ - INFO - main:6452 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 17:21:13 - __main__ - INFO - main:6453 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 17:21:13 - __main__ - INFO - main:6457 - ✅ Configuration validated
2025-07-11 17:21:14 - __main__ - INFO - main:6481 - ✅ All handlers added successfully
2025-07-11 17:21:14 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 17:21:14 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 17:21:14 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 17:21:29 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 17:21:29 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 17:21:29 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 17:21:29 - __main__ - INFO - init_and_run:6486 - 🚀 Starting bot in long polling mode...
2025-07-11 17:21:29 - __main__ - INFO - init_and_run:6487 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 17:21:29 - __main__ - INFO - init_and_run:6488 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 17:21:29 - __main__ - INFO - init_and_run:6489 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 17:21:30 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:35 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:36 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:40 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:42 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:47 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:49 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:57 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:03 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:12 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:17 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:27:03 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:03 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:04 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:04 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:05 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:05 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:07 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:31:57 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:00 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:00 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:06 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:32:10 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:32:18 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:15:59 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:03 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:04 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:11 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:17 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:19 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:23 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:26 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:32 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:38 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:46 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:17:06 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:17:11 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:19:53 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:19:58 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:20:24 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:20:28 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:22:16 - __main__ - INFO - main:6452 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 06:22:16 - __main__ - INFO - main:6453 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 06:22:16 - __main__ - INFO - main:6457 - ✅ Configuration validated
2025-07-12 06:22:17 - __main__ - INFO - main:6481 - ✅ All handlers added successfully
2025-07-12 06:22:17 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 06:22:17 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 06:22:17 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 06:22:30 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 06:22:30 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 06:22:30 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 06:22:30 - __main__ - INFO - init_and_run:6486 - 🚀 Starting bot in long polling mode...
2025-07-12 06:22:30 - __main__ - INFO - init_and_run:6487 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 06:22:30 - __main__ - INFO - init_and_run:6488 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 06:22:30 - __main__ - INFO - init_and_run:6489 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 06:26:29 - __main__ - INFO - _handle_purchase_approval:3015 - Processing approval for user 1049516929, product type: 'canva_pro'
2025-07-12 06:26:29 - __main__ - INFO - _log_purchase_approval:3105 - Logging purchase approval for user 1049516929, product canva_pro to channel -1002712397029
2025-07-12 06:26:30 - __main__ - INFO - _log_purchase_approval:3165 - Purchase approval logged for user 1049516929, product canva_pro
2025-07-12 06:49:32 - __main__ - INFO - main:6462 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 06:49:32 - __main__ - INFO - main:6463 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 06:49:32 - __main__ - INFO - main:6467 - ✅ Configuration validated
2025-07-12 06:49:32 - __main__ - INFO - main:6472 - ✅ Daily bonus range: 💎8.0 - 💎20.0
2025-07-12 06:49:33 - __main__ - INFO - main:6499 - ✅ All handlers added successfully
2025-07-12 06:49:33 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 06:49:33 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 06:49:33 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 06:49:47 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 06:49:47 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 06:49:48 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 06:49:48 - __main__ - INFO - init_and_run:6504 - 🚀 Starting bot in long polling mode...
2025-07-12 06:49:48 - __main__ - INFO - init_and_run:6505 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 06:49:48 - __main__ - INFO - init_and_run:6506 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 06:49:48 - __main__ - INFO - init_and_run:6507 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 06:49:49 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:53 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:59 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:01 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:06 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:09 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:12 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:16 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:22 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:27 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:35 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:44 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:56 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:00 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:17 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:22 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:48 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:52 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:23 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:27 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:58 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:02 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:33 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:37 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:08 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:12 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:42 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:47 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:17 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:21 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:52 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:56 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:56:27 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:56:31 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:02 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:06 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:37 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:41 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:11 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:16 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:46 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:50 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:21 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:25 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:56 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:00 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:31 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:35 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:05 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:10 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:40 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:44 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:15 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:19 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:50 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:03:25 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:03:29 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:00 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:04 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:34 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:39 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:09 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:13 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:44 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:48 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:06:18 - __main__ - ERROR - handle_photo:791 - Error handling photo: 'NoneType' object has no attribute 'id'
2025-07-12 07:06:18 - __main__ - ERROR - error_handler:6445 - Update Update(channel_post=Message(caption='💰 DAILY CRYPTO UPDATE: 12-07-2025\n\nMajor Cryptocurrencies:\n\n- 🟠 BTC: $117,703.00 | 24h: +1.53% 📈\n\n- 🔷 ETH: $2,965.74 | 24h: +0.59% 📈\n\n- 🟣 SOL: $163.47 | 24h: -0.63% 📉\n\n- 💎 TON: $2.98 | 24h: +1.20% 📈\n\nMarket Highlights:\n\n- 🔥 Trending: #TON at $2.98 | 24h: +1.20%\n\n- 📈 Top Gainer: #M at $0.80 | 24h: +81.18%\n\n- 📉 Top Loser: #BONK at $0.00 | 24h: -6.73%', caption_entities=(MessageEntity(length=31, offset=3, type=<MessageEntityType.BOLD>), MessageEntity(length=23, offset=36, type=<MessageEntityType.ITALIC>), MessageEntity(length=36, offset=63, type=<MessageEntityType.ITALIC>), MessageEntity(length=34, offset=103, type=<MessageEntityType.ITALIC>), MessageEntity(length=32, offset=141, type=<MessageEntityType.ITALIC>), MessageEntity(length=30, offset=177, type=<MessageEntityType.ITALIC>), MessageEntity(length=18, offset=209, type=<MessageEntityType.ITALIC>), MessageEntity(length=13, offset=231, type=<MessageEntityType.ITALIC>), MessageEntity(length=4, offset=244, type=<MessageEntityType.HASHTAG>), MessageEntity(length=4, offset=244, type=<MessageEntityType.ITALIC>), MessageEntity(length=23, offset=248, type=<MessageEntityType.ITALIC>), MessageEntity(length=15, offset=275, type=<MessageEntityType.ITALIC>), MessageEntity(length=2, offset=290, type=<MessageEntityType.HASHTAG>), MessageEntity(length=2, offset=290, type=<MessageEntityType.ITALIC>), MessageEntity(length=24, offset=292, type=<MessageEntityType.ITALIC>), MessageEntity(length=14, offset=320, type=<MessageEntityType.ITALIC>), MessageEntity(length=5, offset=334, type=<MessageEntityType.HASHTAG>), MessageEntity(length=5, offset=334, type=<MessageEntityType.ITALIC>), MessageEntity(length=23, offset=339, type=<MessageEntityType.ITALIC>)), channel_chat_created=False, chat=Chat(id=-1001675894903, title="Dev's Crypto Channel 🪐", type=<ChatType.CHANNEL>, username='crypto_by_dev'), date=datetime.datetime(2025, 7, 12, 1, 36, 17, tzinfo=<UTC>), delete_chat_photo=False, group_chat_created=False, message_id=220, photo=(PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADcwADNgQ', file_size=1808, file_unique_id='AQADssgxG-iwUFd4', height=90, width=90), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADbQADNgQ', file_size=22519, file_unique_id='AQADssgxG-iwUFdy', height=320, width=320), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADeAADNgQ', file_size=82164, file_unique_id='AQADssgxG-iwUFd9', height=800, width=800), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADeQADNgQ', file_size=104421, file_unique_id='AQADssgxG-iwUFd-', height=1024, width=1024)), sender_chat=Chat(id=-1001675894903, title="Dev's Crypto Channel 🪐", type=<ChatType.CHANNEL>, username='crypto_by_dev'), supergroup_chat_created=False), update_id=388447795) caused error 'NoneType' object has no attribute 'reply_text'
2025-07-12 07:06:19 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:06:23 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:22:32 - __main__ - INFO - main:6462 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 07:22:32 - __main__ - INFO - main:6463 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 07:22:32 - __main__ - INFO - main:6467 - ✅ Configuration validated
2025-07-12 07:22:32 - __main__ - INFO - main:6472 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 07:22:33 - __main__ - INFO - main:6499 - ✅ All handlers added successfully
2025-07-12 07:22:33 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 07:22:33 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 07:22:33 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 07:22:47 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 07:22:47 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 07:22:47 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 07:22:47 - __main__ - INFO - init_and_run:6504 - 🚀 Starting bot in long polling mode...
2025-07-12 07:22:47 - __main__ - INFO - init_and_run:6505 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 07:22:47 - __main__ - INFO - init_and_run:6506 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 07:22:47 - __main__ - INFO - init_and_run:6507 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 07:51:44 - __main__ - INFO - main:6462 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 07:51:44 - __main__ - INFO - main:6463 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 07:51:44 - __main__ - INFO - main:6467 - ✅ Configuration validated
2025-07-12 07:51:44 - __main__ - INFO - main:6472 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 07:51:45 - __main__ - INFO - main:6499 - ✅ All handlers added successfully
2025-07-12 07:51:45 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 07:51:45 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 07:51:45 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 07:52:00 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 07:52:00 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 07:52:00 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 07:52:00 - __main__ - INFO - init_and_run:6504 - 🚀 Starting bot in long polling mode...
2025-07-12 07:52:00 - __main__ - INFO - init_and_run:6505 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 07:52:00 - __main__ - INFO - init_and_run:6506 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 07:52:00 - __main__ - INFO - init_and_run:6507 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 07:56:05 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-12 07:56:05 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-12 07:56:06 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-12 07:56:06 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1001296547211: administrator
2025-07-12 07:56:06 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-12 07:56:07 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-12 07:56:07 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1002414699235: creator
2025-07-12 07:56:07 - __main__ - INFO - _verify_required_channels:260 - ✅ User 8153676253 verified as member of all required channels
2025-07-12 08:06:09 - __main__ - INFO - main:6466 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:06:09 - __main__ - INFO - main:6467 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:06:09 - __main__ - INFO - main:6471 - ✅ Configuration validated
2025-07-12 08:06:09 - __main__ - INFO - main:6476 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:06:10 - __main__ - INFO - main:6503 - ✅ All handlers added successfully
2025-07-12 08:06:10 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:06:10 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:06:10 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:06:24 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:06:24 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:06:24 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:06:24 - __main__ - INFO - init_and_run:6508 - 🚀 Starting bot in long polling mode...
2025-07-12 08:06:24 - __main__ - INFO - init_and_run:6509 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:06:24 - __main__ - INFO - init_and_run:6510 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:06:24 - __main__ - INFO - init_and_run:6511 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:07:44 - __main__ - INFO - main:6466 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:07:44 - __main__ - INFO - main:6467 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:07:44 - __main__ - INFO - main:6471 - ✅ Configuration validated
2025-07-12 08:07:44 - __main__ - INFO - main:6476 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:07:44 - __main__ - INFO - main:6503 - ✅ All handlers added successfully
2025-07-12 08:07:44 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:07:44 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:07:44 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:07:59 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:07:59 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:07:59 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:07:59 - __main__ - INFO - init_and_run:6508 - 🚀 Starting bot in long polling mode...
2025-07-12 08:07:59 - __main__ - INFO - init_and_run:6509 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:07:59 - __main__ - INFO - init_and_run:6510 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:07:59 - __main__ - INFO - init_and_run:6511 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:09:04 - __main__ - INFO - main:6466 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:09:04 - __main__ - INFO - main:6467 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:09:04 - __main__ - INFO - main:6471 - ✅ Configuration validated
2025-07-12 08:09:04 - __main__ - INFO - main:6476 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:09:04 - __main__ - INFO - main:6503 - ✅ All handlers added successfully
2025-07-12 08:09:04 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:09:04 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:09:04 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:09:20 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:09:20 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:09:20 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:09:20 - __main__ - INFO - init_and_run:6508 - 🚀 Starting bot in long polling mode...
2025-07-12 08:09:20 - __main__ - INFO - init_and_run:6509 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:09:20 - __main__ - INFO - init_and_run:6510 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:09:20 - __main__ - INFO - init_and_run:6511 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:10:20 - __main__ - INFO - main:6466 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:10:20 - __main__ - INFO - main:6467 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:10:20 - __main__ - INFO - main:6471 - ✅ Configuration validated
2025-07-12 08:10:20 - __main__ - INFO - main:6476 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:10:20 - __main__ - INFO - main:6503 - ✅ All handlers added successfully
2025-07-12 08:10:20 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:10:20 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:10:20 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:10:35 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:10:35 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:10:35 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:10:35 - __main__ - INFO - init_and_run:6508 - 🚀 Starting bot in long polling mode...
2025-07-12 08:10:35 - __main__ - INFO - init_and_run:6509 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:10:35 - __main__ - INFO - init_and_run:6510 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:10:35 - __main__ - INFO - init_and_run:6511 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:14:04 - __main__ - INFO - main:6468 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:14:04 - __main__ - INFO - main:6469 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:14:04 - __main__ - INFO - main:6473 - ✅ Configuration validated
2025-07-12 08:14:04 - __main__ - INFO - main:6478 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:14:05 - __main__ - INFO - main:6505 - ✅ All handlers added successfully
2025-07-12 08:14:05 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:14:05 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:14:05 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:14:13 - __main__ - INFO - main:6527 - 👋 Bot stopped by user
2025-07-12 08:14:18 - __main__ - INFO - main:6468 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:14:18 - __main__ - INFO - main:6469 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:14:18 - __main__ - INFO - main:6473 - ✅ Configuration validated
2025-07-12 08:14:18 - __main__ - INFO - main:6478 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:14:19 - __main__ - INFO - main:6505 - ✅ All handlers added successfully
2025-07-12 08:14:19 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:14:19 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:14:19 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:14:32 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:14:32 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:14:32 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:14:32 - __main__ - INFO - init_and_run:6510 - 🚀 Starting bot in long polling mode...
2025-07-12 08:14:32 - __main__ - INFO - init_and_run:6511 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:14:32 - __main__ - INFO - init_and_run:6512 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:14:32 - __main__ - INFO - init_and_run:6513 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:21:06 - __main__ - INFO - main:6527 - 👋 Bot stopped by user
2025-07-12 08:21:11 - __main__ - INFO - main:6468 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:21:11 - __main__ - INFO - main:6469 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:21:11 - __main__ - INFO - main:6473 - ✅ Configuration validated
2025-07-12 08:21:11 - __main__ - INFO - main:6478 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:21:11 - __main__ - INFO - main:6505 - ✅ All handlers added successfully
2025-07-12 08:21:11 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:21:11 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:21:11 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:21:25 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:21:25 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:21:25 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:21:25 - __main__ - INFO - init_and_run:6510 - 🚀 Starting bot in long polling mode...
2025-07-12 08:21:25 - __main__ - INFO - init_and_run:6511 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:21:25 - __main__ - INFO - init_and_run:6512 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:21:25 - __main__ - INFO - init_and_run:6513 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:29:03 - __main__ - INFO - main:6470 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:29:03 - __main__ - INFO - main:6471 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:29:03 - __main__ - INFO - main:6475 - ✅ Configuration validated
2025-07-12 08:29:03 - __main__ - INFO - main:6480 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:29:04 - __main__ - INFO - main:6507 - ✅ All handlers added successfully
2025-07-12 08:29:04 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:29:04 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:29:04 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:29:19 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:29:19 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:29:19 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:29:19 - __main__ - INFO - init_and_run:6512 - 🚀 Starting bot in long polling mode...
2025-07-12 08:29:19 - __main__ - INFO - init_and_run:6513 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:29:19 - __main__ - INFO - init_and_run:6514 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:29:19 - __main__ - INFO - init_and_run:6515 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:30:45 - __main__ - INFO - main:6470 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:30:45 - __main__ - INFO - main:6471 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:30:45 - __main__ - INFO - main:6475 - ✅ Configuration validated
2025-07-12 08:30:45 - __main__ - INFO - main:6480 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:30:46 - __main__ - INFO - main:6507 - ✅ All handlers added successfully
2025-07-12 08:30:46 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:30:46 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:30:46 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:31:10 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:31:10 - __main__ - ERROR - initialize_async_components:84 - ❌ Failed to initialize async components: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 08:31:10 - __main__ - ERROR - main:6531 - ❌ Bot error: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 08:32:39 - __main__ - INFO - main:6470 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:32:39 - __main__ - INFO - main:6471 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:32:39 - __main__ - INFO - main:6475 - ✅ Configuration validated
2025-07-12 08:32:39 - __main__ - INFO - main:6480 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:32:40 - __main__ - INFO - main:6507 - ✅ All handlers added successfully
2025-07-12 08:32:40 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:32:40 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:32:40 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:32:56 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:32:56 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:32:56 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:32:56 - __main__ - INFO - init_and_run:6512 - 🚀 Starting bot in long polling mode...
2025-07-12 08:32:56 - __main__ - INFO - init_and_run:6513 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:32:56 - __main__ - INFO - init_and_run:6514 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:32:56 - __main__ - INFO - init_and_run:6515 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:35:32 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 7464736260
2025-07-12 08:35:32 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-12 08:35:33 - __main__ - INFO - _verify_required_channels:220 - User 7464736260 status in channel -1001296547211: left
2025-07-12 08:35:33 - __main__ - INFO - _verify_required_channels:228 - ❌ User 7464736260 rejected - invalid status in channel -1001296547211: left
2025-07-12 08:35:33 - __main__ - INFO - _verify_required_channels:229 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 08:35:43 - __main__ - INFO - _handle_channel_verification_callback:3358 - Verification callback - Message type: text=False, caption=True
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 7464736260
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:220 - User 7464736260 status in channel -1001296547211: member
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:234 - ✅ User 7464736260 accepted - valid status in channel -1001296547211: member
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:220 - User 7464736260 status in channel -1002414699235: member
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:234 - ✅ User 7464736260 accepted - valid status in channel -1002414699235: member
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:260 - ✅ User 7464736260 verified as member of all required channels
2025-07-12 08:35:47 - __main__ - WARNING - _handle_channel_verification_callback:3433 - Could not delete verification message
2025-07-12 08:35:51 - __main__ - INFO - _process_referral:3670 - Processed referral: 1049516929 -> 7464736260
2025-07-12 08:42:59 - __main__ - INFO - main:6477 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:42:59 - __main__ - INFO - main:6478 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:42:59 - __main__ - INFO - main:6482 - ✅ Configuration validated
2025-07-12 08:42:59 - __main__ - INFO - main:6487 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:43:00 - __main__ - INFO - main:6514 - ✅ All handlers added successfully
2025-07-12 08:43:00 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:43:00 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:43:00 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:43:15 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:43:15 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:43:15 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:43:15 - __main__ - INFO - init_and_run:6519 - 🚀 Starting bot in long polling mode...
2025-07-12 08:43:15 - __main__ - INFO - init_and_run:6520 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:43:15 - __main__ - INFO - init_and_run:6521 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:43:15 - __main__ - INFO - init_and_run:6522 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:56:57 - __main__ - INFO - main:6490 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:56:57 - __main__ - INFO - main:6491 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:56:57 - __main__ - INFO - main:6495 - ✅ Configuration validated
2025-07-12 08:56:57 - __main__ - INFO - main:6503 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:56:57 - __main__ - INFO - main:6530 - ✅ All handlers added successfully
2025-07-12 08:56:57 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:56:57 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:56:57 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 08:57:12 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 08:57:12 - __main__ - INFO - initialize_async_components:75 - ✅ Services initialized
2025-07-12 08:57:12 - __main__ - INFO - initialize_async_components:80 - ✅ Configuration monitoring initialized
2025-07-12 08:57:12 - __main__ - INFO - initialize_async_components:85 - ✅ Default products initialized
2025-07-12 08:57:12 - __main__ - INFO - init_and_run:6535 - 🚀 Starting bot in long polling mode...
2025-07-12 08:57:12 - __main__ - INFO - init_and_run:6536 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:57:12 - __main__ - INFO - init_and_run:6537 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:57:12 - __main__ - INFO - init_and_run:6538 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 09:13:47 - __main__ - INFO - main:6507 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:13:47 - __main__ - INFO - main:6508 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:13:47 - __main__ - INFO - main:6512 - ✅ Configuration validated
2025-07-12 09:13:47 - __main__ - INFO - main:6520 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 09:13:48 - __main__ - INFO - main:6547 - ✅ All handlers added successfully
2025-07-12 09:13:48 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:13:48 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:13:48 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 09:14:08 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 09:14:08 - __main__ - ERROR - initialize_async_components:90 - ❌ Failed to initialize async components: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 09:14:08 - __main__ - ERROR - main:6571 - ❌ Bot error: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 09:14:27 - __main__ - INFO - main:6507 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:14:27 - __main__ - INFO - main:6508 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:14:27 - __main__ - INFO - main:6512 - ✅ Configuration validated
2025-07-12 09:14:27 - __main__ - INFO - main:6520 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 09:14:28 - __main__ - INFO - main:6547 - ✅ All handlers added successfully
2025-07-12 09:14:28 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:14:28 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:14:28 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 09:14:48 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 09:14:48 - __main__ - INFO - initialize_async_components:75 - ✅ Services initialized
2025-07-12 09:14:48 - __main__ - INFO - initialize_async_components:80 - ✅ Configuration monitoring initialized
2025-07-12 09:14:48 - __main__ - INFO - initialize_async_components:85 - ✅ Default products initialized
2025-07-12 09:14:48 - __main__ - INFO - init_and_run:6552 - 🚀 Starting bot in long polling mode...
2025-07-12 09:14:48 - __main__ - INFO - init_and_run:6553 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 09:14:48 - __main__ - INFO - init_and_run:6554 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 09:14:48 - __main__ - INFO - init_and_run:6555 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 09:16:37 - __main__ - INFO - main:6507 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:16:37 - __main__ - INFO - main:6508 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:16:37 - __main__ - INFO - main:6512 - ✅ Configuration validated
2025-07-12 09:16:37 - __main__ - INFO - main:6520 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 09:16:38 - __main__ - INFO - main:6547 - ✅ All handlers added successfully
2025-07-12 09:16:38 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:16:38 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:16:38 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 09:16:52 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 09:16:52 - __main__ - INFO - initialize_async_components:75 - ✅ Services initialized
2025-07-12 09:16:52 - __main__ - INFO - initialize_async_components:80 - ✅ Configuration monitoring initialized
2025-07-12 09:16:52 - __main__ - INFO - initialize_async_components:85 - ✅ Default products initialized
2025-07-12 09:16:52 - __main__ - INFO - init_and_run:6552 - 🚀 Starting bot in long polling mode...
2025-07-12 09:16:52 - __main__ - INFO - init_and_run:6553 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 09:16:52 - __main__ - INFO - init_and_run:6554 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 09:16:52 - __main__ - INFO - init_and_run:6555 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 09:34:27 - __main__ - INFO - main:6530 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:34:27 - __main__ - INFO - main:6531 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:34:27 - __main__ - INFO - main:6535 - ✅ Configuration validated
2025-07-12 09:34:27 - __main__ - INFO - main:6543 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 09:34:28 - __main__ - INFO - main:6570 - ✅ All handlers added successfully
2025-07-12 09:34:28 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:34:28 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:34:28 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 09:34:42 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 09:34:42 - __main__ - INFO - initialize_async_components:75 - ✅ Services initialized
2025-07-12 09:34:42 - __main__ - INFO - initialize_async_components:80 - ✅ Configuration monitoring initialized
2025-07-12 09:34:42 - __main__ - INFO - initialize_async_components:85 - ✅ Default products initialized
2025-07-12 09:34:42 - __main__ - INFO - init_and_run:6575 - 🚀 Starting bot in long polling mode...
2025-07-12 09:34:42 - __main__ - INFO - init_and_run:6576 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 09:34:42 - __main__ - INFO - init_and_run:6577 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 09:34:42 - __main__ - INFO - init_and_run:6578 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 09:41:58 - __main__ - INFO - main:6493 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:41:58 - __main__ - INFO - main:6494 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:41:58 - __main__ - INFO - main:6498 - ✅ Configuration validated
2025-07-12 09:41:58 - __main__ - INFO - main:6506 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 09:41:59 - __main__ - INFO - main:6533 - ✅ All handlers added successfully
2025-07-12 09:41:59 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:41:59 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:41:59 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 09:42:13 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 09:42:13 - __main__ - INFO - initialize_async_components:75 - ✅ Services initialized
2025-07-12 09:42:13 - __main__ - INFO - initialize_async_components:80 - ✅ Configuration monitoring initialized
2025-07-12 09:42:13 - __main__ - INFO - initialize_async_components:85 - ✅ Default products initialized
2025-07-12 09:42:13 - __main__ - INFO - init_and_run:6538 - 🚀 Starting bot in long polling mode...
2025-07-12 09:42:13 - __main__ - INFO - init_and_run:6539 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 09:42:13 - __main__ - INFO - init_and_run:6540 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 09:42:13 - __main__ - INFO - init_and_run:6541 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 09:42:36 - __main__ - INFO - _handle_setting_change:4603 - Handling setting change for type: daily
2025-07-12 09:42:43 - __main__ - INFO - handle_message:702 - Processing message from user 8153676253: 10-20
2025-07-12 09:42:43 - __main__ - INFO - handle_message:703 - Context user_data keys: ['admin_setting_change']
2025-07-12 09:42:43 - __main__ - INFO - handle_message:706 - Processing admin setting change: daily
2025-07-12 09:42:48 - __main__ - INFO - _handle_setting_change:4603 - Handling setting change for type: referral
2025-07-12 09:42:51 - __main__ - INFO - handle_message:702 - Processing message from user 8153676253: 15
2025-07-12 09:42:51 - __main__ - INFO - handle_message:703 - Context user_data keys: ['admin_setting_change']
2025-07-12 09:42:51 - __main__ - INFO - handle_message:706 - Processing admin setting change: referral
2025-07-12 09:43:45 - __main__ - INFO - start_command:191 - Performing real-time channel verification for user 7153113576
2025-07-12 09:43:45 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7153113576
2025-07-12 09:43:45 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:43:47 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1001296547211: left
2025-07-12 09:43:47 - __main__ - INFO - _verify_required_channels:231 - ❌ User 7153113576 rejected - invalid status in channel -1001296547211: left
2025-07-12 09:43:47 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 09:44:18 - __main__ - INFO - _handle_channel_verification_callback:3347 - Verification callback - Message type: text=False, caption=True
2025-07-12 09:44:19 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7153113576
2025-07-12 09:44:19 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:44:20 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1001296547211: left
2025-07-12 09:44:20 - __main__ - INFO - _verify_required_channels:231 - ❌ User 7153113576 rejected - invalid status in channel -1001296547211: left
2025-07-12 09:44:20 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 09:44:24 - __main__ - INFO - _handle_channel_verification_callback:3347 - Verification callback - Message type: text=True, caption=False
2025-07-12 09:44:25 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7153113576
2025-07-12 09:44:25 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:44:25 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1001296547211: member
2025-07-12 09:44:25 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7153113576 accepted - valid status in channel -1001296547211: member
2025-07-12 09:44:25 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 2: -1002414699235
2025-07-12 09:44:26 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1002414699235: member
2025-07-12 09:44:26 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7153113576 accepted - valid status in channel -1002414699235: member
2025-07-12 09:44:26 - __main__ - INFO - _verify_required_channels:263 - ✅ User 7153113576 verified as member of all required channels
2025-07-12 09:44:31 - __main__ - INFO - _process_referral:3664 - Processed referral: 8153676253 -> 7153113576
2025-07-12 09:46:24 - __main__ - INFO - start_command:191 - Performing real-time channel verification for user 7153113576
2025-07-12 09:46:24 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7153113576
2025-07-12 09:46:24 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:46:24 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1001296547211: member
2025-07-12 09:46:24 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7153113576 accepted - valid status in channel -1001296547211: member
2025-07-12 09:46:24 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 2: -1002414699235
2025-07-12 09:46:24 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1002414699235: left
2025-07-12 09:46:24 - __main__ - INFO - _verify_required_channels:231 - ❌ User 7153113576 rejected - invalid status in channel -1002414699235: left
2025-07-12 09:46:24 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 09:46:24 - __main__ - INFO - _update_user_channel_status:277 - Updated channel status for user 7153113576: has_joined_channels = False
2025-07-12 09:46:33 - __main__ - INFO - _handle_channel_verification_callback:3347 - Verification callback - Message type: text=False, caption=True
2025-07-12 09:46:34 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7153113576
2025-07-12 09:46:34 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:46:34 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1001296547211: member
2025-07-12 09:46:34 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7153113576 accepted - valid status in channel -1001296547211: member
2025-07-12 09:46:34 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 2: -1002414699235
2025-07-12 09:46:35 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1002414699235: member
2025-07-12 09:46:35 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7153113576 accepted - valid status in channel -1002414699235: member
2025-07-12 09:46:35 - __main__ - INFO - _verify_required_channels:263 - ✅ User 7153113576 verified as member of all required channels
2025-07-12 09:46:37 - __main__ - INFO - _update_user_channel_status:277 - Updated channel status for user 7153113576: has_joined_channels = True
2025-07-12 09:46:37 - __main__ - WARNING - _handle_channel_verification_callback:3421 - Could not delete verification message
2025-07-12 09:47:11 - __main__ - INFO - start_command:191 - Performing real-time channel verification for user 7099192086
2025-07-12 09:47:11 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7099192086
2025-07-12 09:47:11 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:47:12 - __main__ - INFO - _verify_required_channels:223 - User 7099192086 status in channel -1001296547211: left
2025-07-12 09:47:12 - __main__ - INFO - _verify_required_channels:231 - ❌ User 7099192086 rejected - invalid status in channel -1001296547211: left
2025-07-12 09:47:12 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 09:47:30 - __main__ - INFO - _handle_channel_verification_callback:3347 - Verification callback - Message type: text=False, caption=True
2025-07-12 09:47:32 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7099192086
2025-07-12 09:47:32 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:47:32 - __main__ - INFO - _verify_required_channels:223 - User 7099192086 status in channel -1001296547211: left
2025-07-12 09:47:32 - __main__ - INFO - _verify_required_channels:231 - ❌ User 7099192086 rejected - invalid status in channel -1001296547211: left
2025-07-12 09:47:32 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 09:47:46 - __main__ - INFO - _handle_channel_verification_callback:3347 - Verification callback - Message type: text=True, caption=False
2025-07-12 09:47:48 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7099192086
2025-07-12 09:47:48 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:47:48 - __main__ - INFO - _verify_required_channels:223 - User 7099192086 status in channel -1001296547211: member
2025-07-12 09:47:48 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7099192086 accepted - valid status in channel -1001296547211: member
2025-07-12 09:47:48 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 2: -1002414699235
2025-07-12 09:47:48 - __main__ - INFO - _verify_required_channels:223 - User 7099192086 status in channel -1002414699235: member
2025-07-12 09:47:48 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7099192086 accepted - valid status in channel -1002414699235: member
2025-07-12 09:47:48 - __main__ - INFO - _verify_required_channels:263 - ✅ User 7099192086 verified as member of all required channels
2025-07-12 09:47:53 - __main__ - INFO - _process_referral:3664 - Processed referral: 7153113576 -> 7099192086
2025-07-12 09:57:57 - __main__ - INFO - start_command:191 - Performing real-time channel verification for user 7153113576
2025-07-12 09:57:57 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7153113576
2025-07-12 09:57:57 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:57:57 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1001296547211: member
2025-07-12 09:57:57 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7153113576 accepted - valid status in channel -1001296547211: member
2025-07-12 09:57:57 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 2: -1002414699235
2025-07-12 09:57:57 - __main__ - INFO - _verify_required_channels:223 - User 7153113576 status in channel -1002414699235: member
2025-07-12 09:57:57 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7153113576 accepted - valid status in channel -1002414699235: member
2025-07-12 09:57:57 - __main__ - INFO - _verify_required_channels:263 - ✅ User 7153113576 verified as member of all required channels
2025-07-12 09:58:34 - __main__ - INFO - start_command:191 - Performing real-time channel verification for user 7099192086
2025-07-12 09:58:34 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7099192086
2025-07-12 09:58:34 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:58:34 - __main__ - INFO - _verify_required_channels:223 - User 7099192086 status in channel -1001296547211: left
2025-07-12 09:58:34 - __main__ - INFO - _verify_required_channels:231 - ❌ User 7099192086 rejected - invalid status in channel -1001296547211: left
2025-07-12 09:58:34 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 09:59:12 - __main__ - INFO - _handle_channel_verification_callback:3347 - Verification callback - Message type: text=False, caption=True
2025-07-12 09:59:14 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7099192086
2025-07-12 09:59:14 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:59:14 - __main__ - INFO - _verify_required_channels:223 - User 7099192086 status in channel -1001296547211: left
2025-07-12 09:59:14 - __main__ - INFO - _verify_required_channels:231 - ❌ User 7099192086 rejected - invalid status in channel -1001296547211: left
2025-07-12 09:59:14 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 09:59:25 - __main__ - INFO - _handle_channel_verification_callback:3347 - Verification callback - Message type: text=True, caption=False
2025-07-12 09:59:27 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7099192086
2025-07-12 09:59:27 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 09:59:27 - __main__ - INFO - _verify_required_channels:223 - User 7099192086 status in channel -1001296547211: member
2025-07-12 09:59:27 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7099192086 accepted - valid status in channel -1001296547211: member
2025-07-12 09:59:27 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 2: -1002414699235
2025-07-12 09:59:27 - __main__ - INFO - _verify_required_channels:223 - User 7099192086 status in channel -1002414699235: member
2025-07-12 09:59:27 - __main__ - INFO - _verify_required_channels:237 - ✅ User 7099192086 accepted - valid status in channel -1002414699235: member
2025-07-12 09:59:27 - __main__ - INFO - _verify_required_channels:263 - ✅ User 7099192086 verified as member of all required channels
2025-07-12 10:00:39 - __main__ - INFO - start_command:191 - Performing real-time channel verification for user 5342463568
2025-07-12 10:00:39 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 5342463568
2025-07-12 10:00:39 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 10:00:40 - __main__ - INFO - _verify_required_channels:223 - User 5342463568 status in channel -1001296547211: left
2025-07-12 10:00:40 - __main__ - INFO - _verify_required_channels:231 - ❌ User 5342463568 rejected - invalid status in channel -1001296547211: left
2025-07-12 10:00:40 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 10:00:57 - __main__ - INFO - _handle_channel_verification_callback:3347 - Verification callback - Message type: text=False, caption=True
2025-07-12 10:00:59 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 5342463568
2025-07-12 10:00:59 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 10:00:59 - __main__ - INFO - _verify_required_channels:223 - User 5342463568 status in channel -1001296547211: member
2025-07-12 10:00:59 - __main__ - INFO - _verify_required_channels:237 - ✅ User 5342463568 accepted - valid status in channel -1001296547211: member
2025-07-12 10:00:59 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 2: -1002414699235
2025-07-12 10:00:59 - __main__ - INFO - _verify_required_channels:223 - User 5342463568 status in channel -1002414699235: member
2025-07-12 10:00:59 - __main__ - INFO - _verify_required_channels:237 - ✅ User 5342463568 accepted - valid status in channel -1002414699235: member
2025-07-12 10:00:59 - __main__ - INFO - _verify_required_channels:263 - ✅ User 5342463568 verified as member of all required channels
2025-07-12 10:01:01 - __main__ - WARNING - _handle_channel_verification_callback:3421 - Could not delete verification message
2025-07-12 10:01:04 - __main__ - INFO - _process_referral:3664 - Processed referral: 7153113576 -> 5342463568
2025-07-12 10:08:40 - __main__ - INFO - start_command:191 - Performing real-time channel verification for user 7320385982
2025-07-12 10:08:40 - __main__ - INFO - _verify_required_channels:214 - Starting channel verification for user 7320385982
2025-07-12 10:08:40 - __main__ - INFO - _verify_required_channels:218 - Checking membership in channel 1: -1001296547211
2025-07-12 10:08:41 - __main__ - INFO - _verify_required_channels:223 - User 7320385982 status in channel -1001296547211: left
2025-07-12 10:08:41 - __main__ - INFO - _verify_required_channels:231 - ❌ User 7320385982 rejected - invalid status in channel -1001296547211: left
2025-07-12 10:08:41 - __main__ - INFO - _verify_required_channels:232 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 10:18:54 - __main__ - INFO - main:6542 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 10:18:54 - __main__ - INFO - main:6543 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 10:18:54 - __main__ - INFO - main:6547 - ✅ Configuration validated
2025-07-12 10:18:54 - __main__ - INFO - main:6555 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 10:18:55 - __main__ - INFO - main:6582 - ✅ All handlers added successfully
2025-07-12 10:18:55 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 10:18:55 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 10:18:55 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 10:19:08 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 10:19:08 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 10:19:08 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 10:19:08 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 10:19:08 - __main__ - INFO - init_and_run:6587 - 🚀 Starting bot in long polling mode...
2025-07-12 10:19:08 - __main__ - INFO - init_and_run:6588 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 10:19:08 - __main__ - INFO - init_and_run:6589 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 10:19:08 - __main__ - INFO - init_and_run:6590 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 10:19:28 - __main__ - INFO - start_command:210 - 🔄 ATTEMPTING to store user data immediately for user 7320385982
2025-07-12 10:19:28 - __main__ - INFO - start_command:211 - 📊 User data: {'user_id': 7320385982, 'username': None, 'first_name': 'Raghvendra', 'last_name': None, 'language_code': 'en', 'is_bot': False, 'is_premium': None}
2025-07-12 10:19:28 - __main__ - INFO - start_command:212 - 🔗 Referral code: JSMNAIIU
2025-07-12 10:19:28 - __main__ - INFO - start_command:217 - ✅ SUCCESS: User data stored immediately for user 7320385982
2025-07-12 10:19:28 - __main__ - INFO - start_command:218 - 📋 Created user: 7320385982, referred_by: 8153676253
2025-07-12 10:19:28 - __main__ - INFO - start_command:229 - Performing real-time channel verification for user 7320385982
2025-07-12 10:19:28 - __main__ - INFO - _verify_required_channels:263 - Starting channel verification for user 7320385982
2025-07-12 10:19:28 - __main__ - INFO - _verify_required_channels:267 - Checking membership in channel 1: -1001296547211
2025-07-12 10:19:29 - __main__ - INFO - _verify_required_channels:272 - User 7320385982 status in channel -1001296547211: left
2025-07-12 10:19:29 - __main__ - INFO - _verify_required_channels:280 - ❌ User 7320385982 rejected - invalid status in channel -1001296547211: left
2025-07-12 10:19:29 - __main__ - INFO - _verify_required_channels:281 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 10:19:29 - __main__ - INFO - _update_user_channel_status:326 - Updated channel status for user 7320385982: has_joined_channels = False
2025-07-12 10:19:55 - __main__ - INFO - _handle_channel_verification_callback:3382 - Verification callback - Message type: text=False, caption=True
2025-07-12 10:19:56 - __main__ - INFO - _verify_required_channels:263 - Starting channel verification for user 7320385982
2025-07-12 10:19:56 - __main__ - INFO - _verify_required_channels:267 - Checking membership in channel 1: -1001296547211
2025-07-12 10:19:56 - __main__ - INFO - _verify_required_channels:272 - User 7320385982 status in channel -1001296547211: member
2025-07-12 10:19:56 - __main__ - INFO - _verify_required_channels:286 - ✅ User 7320385982 accepted - valid status in channel -1001296547211: member
2025-07-12 10:19:56 - __main__ - INFO - _verify_required_channels:267 - Checking membership in channel 2: -1002414699235
2025-07-12 10:19:57 - __main__ - INFO - _verify_required_channels:272 - User 7320385982 status in channel -1002414699235: left
2025-07-12 10:19:57 - __main__ - INFO - _verify_required_channels:280 - ❌ User 7320385982 rejected - invalid status in channel -1002414699235: left
2025-07-12 10:19:57 - __main__ - INFO - _verify_required_channels:281 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 10:20:09 - __main__ - INFO - _handle_channel_verification_callback:3382 - Verification callback - Message type: text=True, caption=False
2025-07-12 10:20:10 - __main__ - INFO - _verify_required_channels:263 - Starting channel verification for user 7320385982
2025-07-12 10:20:10 - __main__ - INFO - _verify_required_channels:267 - Checking membership in channel 1: -1001296547211
2025-07-12 10:20:10 - __main__ - INFO - _verify_required_channels:272 - User 7320385982 status in channel -1001296547211: member
2025-07-12 10:20:10 - __main__ - INFO - _verify_required_channels:286 - ✅ User 7320385982 accepted - valid status in channel -1001296547211: member
2025-07-12 10:20:10 - __main__ - INFO - _verify_required_channels:267 - Checking membership in channel 2: -1002414699235
2025-07-12 10:20:11 - __main__ - INFO - _verify_required_channels:272 - User 7320385982 status in channel -1002414699235: member
2025-07-12 10:20:11 - __main__ - INFO - _verify_required_channels:286 - ✅ User 7320385982 accepted - valid status in channel -1002414699235: member
2025-07-12 10:20:11 - __main__ - INFO - _verify_required_channels:312 - ✅ User 7320385982 verified as member of all required channels
2025-07-12 10:20:12 - __main__ - INFO - _update_user_channel_status:326 - Updated channel status for user 7320385982: has_joined_channels = True
2025-07-12 10:20:12 - __main__ - INFO - _handle_channel_verification_callback:3440 - Processing pending referral for user 7320385982 after channel verification
2025-07-12 10:20:13 - __main__ - INFO - _process_referral:3713 - Processed referral: 8153676253 -> 7320385982
2025-07-12 10:22:39 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: <EMAIL>
2025-07-12 10:22:39 - __main__ - INFO - handle_message:735 - Context user_data keys: ['awaiting_email', 'purchase_data']
2025-07-12 10:22:43 - __main__ - INFO - _handle_purchase_approval:3054 - Processing approval for user 8153676253, product type: 'canva_pro'
2025-07-12 10:22:43 - __main__ - INFO - _log_purchase_approval:3144 - Logging purchase approval for user 8153676253, product canva_pro to channel -1002712397029
2025-07-12 10:22:44 - __main__ - INFO - _log_purchase_approval:3204 - Purchase approval logged for user 8153676253, product canva_pro
2025-07-12 10:22:59 - __main__ - INFO - _handle_setting_change:4652 - Handling setting change for type: referral
2025-07-12 10:23:04 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: 10
2025-07-12 10:23:04 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_setting_change']
2025-07-12 10:23:04 - __main__ - INFO - handle_message:738 - Processing admin setting change: referral
2025-07-12 10:23:21 - __main__ - INFO - _handle_setting_change:4652 - Handling setting change for type: welcome
2025-07-12 10:23:27 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: 100
2025-07-12 10:23:27 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_setting_change']
2025-07-12 10:23:27 - __main__ - INFO - handle_message:738 - Processing admin setting change: welcome
2025-07-12 10:23:52 - __main__ - INFO - _handle_setting_change:4652 - Handling setting change for type: withdrawal
2025-07-12 10:23:57 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: 400
2025-07-12 10:23:57 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_setting_change']
2025-07-12 10:23:57 - __main__ - INFO - handle_message:738 - Processing admin setting change: withdrawal
2025-07-12 10:36:25 - __main__ - INFO - main:6571 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 10:36:25 - __main__ - INFO - main:6572 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 10:36:25 - __main__ - INFO - main:6576 - ✅ Configuration validated
2025-07-12 10:36:25 - __main__ - INFO - main:6584 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 10:36:26 - __main__ - INFO - main:6611 - ✅ All handlers added successfully
2025-07-12 10:36:26 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 10:36:26 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 10:36:26 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 10:36:40 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 10:36:40 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 10:36:40 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 10:36:40 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 10:36:40 - __main__ - INFO - init_and_run:6616 - 🚀 Starting bot in long polling mode...
2025-07-12 10:36:40 - __main__ - INFO - init_and_run:6617 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 10:36:40 - __main__ - INFO - init_and_run:6618 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 10:36:40 - __main__ - INFO - init_and_run:6619 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 10:37:40 - __main__ - INFO - _send_admin_task_notification:933 - Admin notification sent for submission sub_af938ffac124 from user 7099192086
2025-07-12 10:38:43 - __main__ - ERROR - handle_callback:2213 - Error handling callback: There is no text in the message to edit
2025-07-12 10:39:00 - __main__ - ERROR - handle_callback:2213 - Error handling callback: There is no text in the message to edit
2025-07-12 10:39:16 - __main__ - ERROR - handle_callback:2213 - Error handling callback: There is no text in the message to edit
2025-07-12 11:06:30 - __main__ - INFO - main:6640 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:06:30 - __main__ - INFO - main:6641 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:06:30 - __main__ - INFO - main:6645 - ✅ Configuration validated
2025-07-12 11:06:30 - __main__ - INFO - main:6653 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 11:06:31 - __main__ - INFO - main:6680 - ✅ All handlers added successfully
2025-07-12 11:06:31 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:06:31 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:06:31 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 11:06:45 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 11:06:45 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 11:06:45 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 11:06:45 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 11:06:45 - __main__ - INFO - init_and_run:6685 - 🚀 Starting bot in long polling mode...
2025-07-12 11:06:45 - __main__ - INFO - init_and_run:6686 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 11:06:45 - __main__ - INFO - init_and_run:6687 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 11:06:45 - __main__ - INFO - init_and_run:6688 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 11:11:09 - __main__ - INFO - main:6640 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:11:09 - __main__ - INFO - main:6641 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:11:09 - __main__ - INFO - main:6645 - ✅ Configuration validated
2025-07-12 11:11:09 - __main__ - INFO - main:6653 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 11:11:09 - __main__ - INFO - main:6680 - ✅ All handlers added successfully
2025-07-12 11:11:09 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:11:09 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:11:09 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 11:11:24 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 11:11:24 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 11:11:24 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 11:11:24 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 11:11:24 - __main__ - INFO - init_and_run:6685 - 🚀 Starting bot in long polling mode...
2025-07-12 11:11:24 - __main__ - INFO - init_and_run:6686 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 11:11:24 - __main__ - INFO - init_and_run:6687 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 11:11:24 - __main__ - INFO - init_and_run:6688 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 11:12:01 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: vvvvvvvvvvvvv
2025-07-12 11:12:01 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_task_creation']
2025-07-12 11:12:25 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: khbk0
2025-07-12 11:12:25 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_task_creation']
2025-07-12 11:12:29 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: 800
2025-07-12 11:12:29 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_task_creation']
2025-07-12 11:12:32 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: 500
2025-07-12 11:12:32 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_task_creation']
2025-07-12 11:12:32 - __main__ - INFO - _show_task_creation_summary:5158 - Task creation summary data: {'step': 'reward', 'button_name': 'vvvvvvvvvvvvv', 'task_type': 'submit_image', 'reference_image_url': 'AgACAgUAAxkBAAID9mhx9br6h3pQMOZCQydqA0gK7zJkAAL5xTEbNt6QV2L3Gy1PSBowAQADAgADeQADNgQ', 'instructions': 'khbk0', 'reward': 500.0}
2025-07-12 11:12:51 - __main__ - INFO - _send_admin_task_notification:933 - Admin notification sent for submission sub_71dc1a2eb283 from user 8153676253
2025-07-12 11:13:03 - __main__ - ERROR - handle_callback:2271 - Error handling callback: There is no text in the message to edit
2025-07-12 11:23:15 - __main__ - INFO - main:6647 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:23:15 - __main__ - INFO - main:6648 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:23:15 - __main__ - INFO - main:6652 - ✅ Configuration validated
2025-07-12 11:23:15 - __main__ - INFO - main:6660 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 11:23:16 - __main__ - INFO - main:6687 - ✅ All handlers added successfully
2025-07-12 11:23:16 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:23:16 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:23:16 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 11:23:29 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 11:23:29 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 11:23:29 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 11:23:29 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 11:23:29 - __main__ - INFO - init_and_run:6692 - 🚀 Starting bot in long polling mode...
2025-07-12 11:23:29 - __main__ - INFO - init_and_run:6693 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 11:23:29 - __main__ - INFO - init_and_run:6694 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 11:23:29 - __main__ - INFO - init_and_run:6695 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 11:24:55 - __main__ - INFO - _send_admin_task_notification:940 - Admin notification sent for submission sub_68696537c7b6 from user 1049516929
2025-07-12 11:35:36 - __main__ - INFO - main:6665 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:35:36 - __main__ - INFO - main:6666 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:35:36 - __main__ - INFO - main:6670 - ✅ Configuration validated
2025-07-12 11:35:36 - __main__ - INFO - main:6678 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 11:35:37 - __main__ - INFO - main:6705 - ✅ All handlers added successfully
2025-07-12 11:35:37 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:35:37 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:35:37 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 11:35:50 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 11:35:50 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 11:35:50 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 11:35:50 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 11:35:50 - __main__ - INFO - init_and_run:6710 - 🚀 Starting bot in long polling mode...
2025-07-12 11:35:50 - __main__ - INFO - init_and_run:6711 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 11:35:50 - __main__ - INFO - init_and_run:6712 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 11:35:50 - __main__ - INFO - init_and_run:6713 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 11:44:05 - __main__ - INFO - main:6665 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:44:05 - __main__ - INFO - main:6666 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:44:05 - __main__ - INFO - main:6670 - ✅ Configuration validated
2025-07-12 11:44:05 - __main__ - INFO - main:6678 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 11:44:06 - __main__ - INFO - main:6705 - ✅ All handlers added successfully
2025-07-12 11:44:06 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:44:06 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:44:06 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 11:44:20 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 11:44:20 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 11:44:20 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 11:44:20 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 11:44:20 - __main__ - INFO - init_and_run:6710 - 🚀 Starting bot in long polling mode...
2025-07-12 11:44:20 - __main__ - INFO - init_and_run:6711 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 11:44:20 - __main__ - INFO - init_and_run:6712 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 11:44:20 - __main__ - INFO - init_and_run:6713 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 11:45:05 - __main__ - INFO - handle_callback:2294 - Admin 8153676253 requested delete for task: task_3ad7bd244c7a
2025-07-12 11:45:05 - __main__ - INFO - _show_delete_confirmation:5876 - _show_delete_confirmation called with task_id: task_3ad7bd244c7a
2025-07-12 11:45:05 - __main__ - INFO - _show_delete_confirmation:5890 - Task found for deletion: dksfhsa
2025-07-12 11:45:07 - __main__ - INFO - _delete_task:5950 - Admin 8153676253 deleted task task_3ad7bd244c7a (dksfhsa)
2025-07-12 11:45:11 - __main__ - INFO - handle_callback:2294 - Admin 8153676253 requested delete for task: task_844ea611e669
2025-07-12 11:45:11 - __main__ - INFO - _show_delete_confirmation:5876 - _show_delete_confirmation called with task_id: task_844ea611e669
2025-07-12 11:45:11 - __main__ - INFO - _show_delete_confirmation:5890 - Task found for deletion: BOT OPENING
2025-07-12 11:45:13 - __main__ - INFO - _delete_task:5950 - Admin 8153676253 deleted task task_844ea611e669 (BOT OPENING)
2025-07-12 11:45:16 - __main__ - INFO - handle_callback:2294 - Admin 8153676253 requested delete for task: task_b8641535c488
2025-07-12 11:45:16 - __main__ - INFO - _show_delete_confirmation:5876 - _show_delete_confirmation called with task_id: task_b8641535c488
2025-07-12 11:45:16 - __main__ - INFO - _show_delete_confirmation:5890 - Task found for deletion: vvvvvvvvvvvvv
2025-07-12 11:45:18 - __main__ - INFO - _delete_task:5950 - Admin 8153676253 deleted task task_b8641535c488 (vvvvvvvvvvvvv)
2025-07-12 11:45:32 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: Hii
2025-07-12 11:45:32 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_task_creation']
2025-07-12 11:45:42 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: Bznzn
2025-07-12 11:45:42 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_task_creation']
2025-07-12 11:45:49 - __main__ - INFO - handle_message:734 - Processing message from user 8153676253: 466
2025-07-12 11:45:49 - __main__ - INFO - handle_message:735 - Context user_data keys: ['admin_task_creation']
2025-07-12 11:45:49 - __main__ - INFO - _show_task_creation_summary:5183 - Task creation summary data: {'step': 'reward', 'button_name': 'Hii', 'task_type': 'submit_image', 'reference_image_url': 'AgACAgUAAxkBAAIEIGhx_YgWJ9MqIHdY_MabX-hIgGfVAAL_xTEbNt6QV1dNpXaGNxZCAQADAgADeQADNgQ', 'instructions': 'Bznzn', 'reward': 466.0}
2025-07-12 11:46:08 - __main__ - INFO - _send_admin_task_notification:940 - Admin notification sent for submission sub_2fce910cc645 from user 8153676253
2025-07-12 11:47:50 - __main__ - INFO - main:6667 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:47:50 - __main__ - INFO - main:6668 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:47:50 - __main__ - INFO - main:6672 - ✅ Configuration validated
2025-07-12 11:47:50 - __main__ - INFO - main:6680 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 11:47:50 - __main__ - INFO - main:6707 - ✅ All handlers added successfully
2025-07-12 11:47:50 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:47:50 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:47:50 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 11:48:03 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 11:48:03 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 11:48:03 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 11:48:03 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 11:48:03 - __main__ - INFO - init_and_run:6712 - 🚀 Starting bot in long polling mode...
2025-07-12 11:48:03 - __main__ - INFO - init_and_run:6713 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 11:48:03 - __main__ - INFO - init_and_run:6714 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 11:48:03 - __main__ - INFO - init_and_run:6715 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 11:49:31 - __main__ - INFO - main:6667 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:49:31 - __main__ - INFO - main:6668 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:49:31 - __main__ - INFO - main:6672 - ✅ Configuration validated
2025-07-12 11:49:31 - __main__ - INFO - main:6680 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 11:49:31 - __main__ - INFO - main:6707 - ✅ All handlers added successfully
2025-07-12 11:49:31 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:49:31 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:49:31 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 11:49:45 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 11:49:45 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 11:49:45 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 11:49:45 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 11:49:45 - __main__ - INFO - init_and_run:6712 - 🚀 Starting bot in long polling mode...
2025-07-12 11:49:45 - __main__ - INFO - init_and_run:6713 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 11:49:45 - __main__ - INFO - init_and_run:6714 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 11:49:45 - __main__ - INFO - init_and_run:6715 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 11:50:11 - __main__ - INFO - _send_admin_task_notification:940 - Admin notification sent for submission sub_f2422ae3448a from user 1049516929
2025-07-12 11:50:17 - __main__ - WARNING - _disapprove_task_submission:1123 - Failed to notify user 1049516929 of rejection: cannot access local variable 'rejected_count' where it is not associated with a value
2025-07-12 11:50:17 - __main__ - INFO - _disapprove_task_submission:1150 - Admin 1381431908 rejected task submission sub_f2422ae3448a for user 1049516929
2025-07-12 11:50:51 - __main__ - INFO - handle_message:734 - Processing message from user 1049516929: 11:50:17 - WARNING - Failed to notify user 1049516929 of rejection: cannot access local variable 'rejected_count' where it is not associated with a value
11:50:17 - INFO - Admin 1381431908 rejected task submission sub_f2422ae3448a for user 1049516929
2025-07-12 11:50:51 - __main__ - INFO - handle_message:735 - Context user_data keys: ['user_task_submission']
2025-07-12 11:50:51 - __main__ - INFO - handle_message:749 - No special context found, showing default message
2025-07-12 11:50:59 - __main__ - INFO - _send_admin_task_notification:940 - Admin notification sent for submission sub_40dad2a6fdee from user 1049516929
2025-07-12 11:51:12 - __main__ - ERROR - _approve_task_submission:1036 - Error approving task submission sub_40dad2a6fdee: TaskService.approve_submission() got an unexpected keyword argument 'reward_amount'
2025-07-12 11:56:07 - __main__ - INFO - main:6670 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:56:07 - __main__ - INFO - main:6671 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:56:07 - __main__ - INFO - main:6675 - ✅ Configuration validated
2025-07-12 11:56:07 - __main__ - INFO - main:6683 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 11:56:07 - __main__ - INFO - main:6710 - ✅ All handlers added successfully
2025-07-12 11:56:07 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 11:56:07 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 11:56:07 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 11:56:20 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 11:56:20 - __main__ - INFO - initialize_async_components:78 - ✅ Services initialized
2025-07-12 11:56:20 - __main__ - INFO - initialize_async_components:83 - ✅ Configuration monitoring initialized
2025-07-12 11:56:20 - __main__ - INFO - initialize_async_components:88 - ✅ Default products initialized
2025-07-12 11:56:20 - __main__ - INFO - init_and_run:6715 - 🚀 Starting bot in long polling mode...
2025-07-12 11:56:20 - __main__ - INFO - init_and_run:6716 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 11:56:20 - __main__ - INFO - init_and_run:6717 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 11:56:20 - __main__ - INFO - init_and_run:6718 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 11:56:29 - __main__ - INFO - start_command:210 - 🔄 ATTEMPTING to store user data immediately for user 1381431908
2025-07-12 11:56:29 - __main__ - INFO - start_command:211 - 📊 User data: {'user_id': 1381431908, 'username': 'devendra_yadv', 'first_name': '.', 'last_name': None, 'language_code': 'en', 'is_bot': False, 'is_premium': None}
2025-07-12 11:56:29 - __main__ - INFO - start_command:212 - 🔗 Referral code: None
2025-07-12 11:56:30 - __main__ - INFO - start_command:217 - ✅ SUCCESS: User data stored immediately for user 1381431908
2025-07-12 11:56:30 - __main__ - INFO - start_command:218 - 📋 Created user: 1381431908, referred_by: None
2025-07-12 11:56:30 - __main__ - INFO - start_command:229 - Performing real-time channel verification for user 1381431908
2025-07-12 11:56:30 - __main__ - INFO - _verify_required_channels:263 - Starting channel verification for user 1381431908
2025-07-12 11:56:30 - __main__ - INFO - _verify_required_channels:267 - Checking membership in channel 1: -1001296547211
2025-07-12 11:56:30 - __main__ - INFO - _verify_required_channels:272 - User 1381431908 status in channel -1001296547211: member
2025-07-12 11:56:30 - __main__ - INFO - _verify_required_channels:286 - ✅ User 1381431908 accepted - valid status in channel -1001296547211: member
2025-07-12 11:56:30 - __main__ - INFO - _verify_required_channels:267 - Checking membership in channel 2: -1002414699235
2025-07-12 11:56:31 - __main__ - INFO - _verify_required_channels:272 - User 1381431908 status in channel -1002414699235: member
2025-07-12 11:56:31 - __main__ - INFO - _verify_required_channels:286 - ✅ User 1381431908 accepted - valid status in channel -1002414699235: member
2025-07-12 11:56:31 - __main__ - INFO - _verify_required_channels:312 - ✅ User 1381431908 verified as member of all required channels
2025-07-12 11:56:31 - __main__ - INFO - _update_user_channel_status:326 - Updated channel status for user 1381431908: has_joined_channels = True
2025-07-12 11:56:40 - __main__ - INFO - _disapprove_task_submission:1153 - Admin 1381431908 rejected task submission sub_40dad2a6fdee for user 1049516929
