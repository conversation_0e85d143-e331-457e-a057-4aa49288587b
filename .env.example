# Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here
BOT_USERNAME=your_bot_username

# Database Configuration
MONGODB_URI=your_mongodb_atlas_connection_string
DATABASE_NAME=referral_bot_db

# Admin Configuration
ADMIN_USER_IDS=123456789,987654321
ADMIN_PASSWORD=your_secure_admin_password

# Bot Settings
REFERRAL_REWARD=10
DAILY_BONUS_AMOUNT=5
MINIMUM_WITHDRAWAL=10
WITHDRAWAL_COOLDOWN_HOURS=24

# Security
SECRET_KEY=your_secret_key_for_encryption
RATE_LIMIT_MESSAGES=20
RATE_LIMIT_WINDOW=60

# Logging
LOG_LEVEL=INFO
LOG_FILE=bot.log

# Channel Settings (Optional - can be managed through admin panel)
REQUIRED_CHANNELS=@channel1,@channel2

# Bot Mode Configuration
# The bot uses long polling by default (no webhook configuration needed)
# Long polling is suitable for development and simple deployments
# No domain, SSL certificates, or public IP required
