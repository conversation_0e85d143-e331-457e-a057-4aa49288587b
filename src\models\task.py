"""
Task models for the comprehensive task management system
"""

from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from enum import Enum

class TaskType(Enum):
    """Task type enumeration"""
    JOIN_CHANNEL = "join_channel"
    SUBMIT_IMAGE = "submit_image"
    SHARE_BOT = "share_bot"
    DAILY_STREAK = "daily_streak"
    REFERRAL_MILESTONE = "referral_milestone"

class TaskStatus(Enum):
    """Task status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DELETED = "deleted"

class UserTaskStatus(Enum):
    """User task completion status"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PENDING_REVIEW = "pending_review"
    COMPLETED = "completed"
    REJECTED = "rejected"

class VerificationMode(Enum):
    """Task verification mode"""
    MANUAL_REVIEW = "manual_review"
    AUTO_APPROVE = "auto_approve"

@dataclass
class Task:
    """Task model representing admin-created tasks"""
    
    task_id: str
    task_name: str
    task_type: TaskType
    reward_amount: float
    is_active: bool = True
    
    # Task-specific configuration
    description: Optional[str] = None
    instructions: Optional[str] = None
    
    # Join Channel Task fields
    channel_id: Optional[str] = None
    channel_username: Optional[str] = None
    join_link: Optional[str] = None
    
    # Image Submission Task fields
    reference_image_url: Optional[str] = None
    verification_mode: VerificationMode = VerificationMode.MANUAL_REVIEW
    
    # Metadata
    created_by: int = None
    created_at: datetime = None
    updated_at: datetime = None
    completion_count: int = 0
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)
        
        if self.updated_at is None:
            self.updated_at = datetime.now(timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary for database storage"""
        return {
            'task_id': self.task_id,
            'task_name': self.task_name,
            'task_type': self.task_type.value,
            'reward_amount': self.reward_amount,
            'is_active': self.is_active,
            'description': self.description,
            'instructions': self.instructions,
            'channel_id': self.channel_id,
            'channel_username': self.channel_username,
            'join_link': self.join_link,
            'reference_image_url': self.reference_image_url,
            'verification_mode': self.verification_mode.value,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'completion_count': self.completion_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """Create task from dictionary"""
        # Remove MongoDB _id field if present
        data = data.copy()
        data.pop('_id', None)

        # Convert string dates back to datetime objects
        if data.get('created_at'):
            data['created_at'] = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
        if data.get('updated_at'):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00'))

        # Convert enum strings back to enums
        if data.get('task_type'):
            data['task_type'] = TaskType(data['task_type'])
        if data.get('verification_mode'):
            data['verification_mode'] = VerificationMode(data['verification_mode'])

        return cls(**data)

@dataclass
class UserTask:
    """User task tracking model"""
    
    user_id: int
    task_id: str
    status: UserTaskStatus = UserTaskStatus.NOT_STARTED
    
    # Submission data
    submission_data: Optional[Dict[str, Any]] = None
    submitted_image_url: Optional[str] = None
    submission_notes: Optional[str] = None
    
    # Review data
    reviewed_by: Optional[int] = None
    review_notes: Optional[str] = None
    rejection_reason: Optional[str] = None
    
    # Timestamps
    started_at: Optional[datetime] = None
    submitted_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    reviewed_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Initialize default values"""
        if self.submission_data is None:
            self.submission_data = {}
    
    def start_task(self):
        """Mark task as started"""
        self.status = UserTaskStatus.IN_PROGRESS
        self.started_at = datetime.now(timezone.utc)
    
    def submit_for_review(self, submission_data: Dict[str, Any] = None, image_url: str = None, notes: str = None):
        """Submit task for review"""
        self.status = UserTaskStatus.PENDING_REVIEW
        self.submitted_at = datetime.now(timezone.utc)
        if submission_data:
            self.submission_data.update(submission_data)
        if image_url:
            self.submitted_image_url = image_url
        if notes:
            self.submission_notes = notes
    
    def complete_task(self, reviewed_by: int = None):
        """Mark task as completed"""
        self.status = UserTaskStatus.COMPLETED
        self.completed_at = datetime.now(timezone.utc)
        if reviewed_by:
            self.reviewed_by = reviewed_by
            self.reviewed_at = datetime.now(timezone.utc)
    
    def reject_task(self, reviewed_by: int, reason: str, notes: str = None):
        """Reject task submission"""
        self.status = UserTaskStatus.REJECTED
        self.reviewed_by = reviewed_by
        self.reviewed_at = datetime.now(timezone.utc)
        self.rejection_reason = reason
        if notes:
            self.review_notes = notes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage"""
        return {
            'user_id': self.user_id,
            'task_id': self.task_id,
            'status': self.status.value,
            'submission_data': self.submission_data,
            'submitted_image_url': self.submitted_image_url,
            'submission_notes': self.submission_notes,
            'reviewed_by': self.reviewed_by,
            'review_notes': self.review_notes,
            'rejection_reason': self.rejection_reason,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'submitted_at': self.submitted_at.isoformat() if self.submitted_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'reviewed_at': self.reviewed_at.isoformat() if self.reviewed_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserTask':
        """Create from dictionary"""
        # Remove MongoDB _id field if present
        data = data.copy()
        data.pop('_id', None)

        # Convert string dates back to datetime objects
        date_fields = ['started_at', 'submitted_at', 'completed_at', 'reviewed_at']
        for field in date_fields:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field].replace('Z', '+00:00'))

        # Convert enum string back to enum
        if data.get('status'):
            data['status'] = UserTaskStatus(data['status'])

        return cls(**data)

@dataclass
class TaskSubmission:
    """Task submission audit model"""

    submission_id: str
    user_id: int
    task_id: str
    submission_type: str  # 'image', 'verification', etc.

    # Submission content
    content_data: Dict[str, Any] = field(default_factory=dict)
    image_urls: List[str] = field(default_factory=list)
    submission_notes: Optional[str] = None  # User-provided notes during submission

    # Review status
    review_status: str = "pending"  # pending, approved, rejected
    reviewed_by: Optional[int] = None
    review_notes: Optional[str] = None  # Admin review notes

    # Timestamps
    submitted_at: datetime = None
    reviewed_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Initialize default values"""
        if self.submitted_at is None:
            self.submitted_at = datetime.now(timezone.utc)
    
    def approve(self, admin_id: int, notes: str = None):
        """Approve submission"""
        self.review_status = "approved"
        self.reviewed_by = admin_id
        self.reviewed_at = datetime.now(timezone.utc)
        if notes:
            self.review_notes = notes
    
    def reject(self, admin_id: int, notes: str):
        """Reject submission"""
        self.review_status = "rejected"
        self.reviewed_by = admin_id
        self.reviewed_at = datetime.now(timezone.utc)
        self.review_notes = notes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage"""
        return {
            'submission_id': self.submission_id,
            'user_id': self.user_id,
            'task_id': self.task_id,
            'submission_type': self.submission_type,
            'content_data': self.content_data,
            'image_urls': self.image_urls,
            'submission_notes': self.submission_notes,
            'review_status': self.review_status,
            'reviewed_by': self.reviewed_by,
            'review_notes': self.review_notes,
            'submitted_at': self.submitted_at.isoformat() if self.submitted_at else None,
            'reviewed_at': self.reviewed_at.isoformat() if self.reviewed_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskSubmission':
        """Create from dictionary"""
        # Remove MongoDB _id field if present
        data = data.copy()
        data.pop('_id', None)

        # Convert string dates back to datetime objects
        if data.get('submitted_at'):
            data['submitted_at'] = datetime.fromisoformat(data['submitted_at'].replace('Z', '+00:00'))
        if data.get('reviewed_at'):
            data['reviewed_at'] = datetime.fromisoformat(data['reviewed_at'].replace('Z', '+00:00'))

        return cls(**data)
