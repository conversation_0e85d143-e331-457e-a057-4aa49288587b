# Telegram Referral Earning Bot 🤖💰

A comprehensive Telegram bot system for referral-based earning with Indian Rupee (💎) rewards, forced channel subscription, and admin management panel.

**🔄 Uses Long Polling - No Domain or SSL Required!**

## 🌟 Features

### User Features
- **Referral System**: Earn 💎10 per successful referral
- **Task Completion**: Earn rewards by completing admin-created tasks
- **Daily Bonus**: Claim daily rewards with cooldown system
- **Balance Tracking**: Real-time balance and transaction history
- **Withdrawal System**: Minimum 💎500 withdrawal threshold
- **Digital Products**: Redeem Canva Pro, Spotify Premium, Netflix, etc.
- **Forced Subscription**: Mandatory channel joining before bot usage
- **Gamification**: Leaderboards, progress tracking, and motivational messages
- **Task Submissions**: Submit screenshots and proof images for verification

### Admin Features
- **Complete Bot Control**: Manage all bot settings and configurations
- **User Management**: View, edit, ban/unban users
- **Task Management**: Create dynamic tasks with join channel and image submission types
- **Admin Review Panel**: Review and approve/reject user task submissions
- **Withdrawal Management**: Approve/reject withdrawal requests
- **Channel Management**: Add/remove required channels
- **Analytics Dashboard**: User stats, earnings, popular products, task statistics
- **Broadcast Messaging**: Send messages to all users
- **Product Catalog**: Manage available digital products

### Task Management System
- **📺 Join Channel Tasks**: Automatic verification via Telegram Bot API
- **📸 Image Submission Tasks**: Manual admin review with approve/reject workflow
- **🎯 Dynamic Task Creation**: Admins create custom tasks with configurable rewards
- **📋 Real-time Status Tracking**: Users see task progress and completion status
- **🔍 Comprehensive Review Interface**: Admin panel for reviewing submissions
- **🛡️ Security & Rate Limiting**: Prevents spam and abuse with proper validation
- **📊 Task Analytics**: Monitor completion rates and reward distribution

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MongoDB Atlas account
- Telegram Bot Token (from @BotFather)
- No domain or SSL certificates required (uses long polling)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd refer-earn-bot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Setup task management system (optional)**
   ```bash
   python setup_task_system.py
   ```

5. **Run the bot**
   ```bash
   python main.py
   # OR use the startup script
   python start_bot.py
   ```

**That's it! No domain, SSL, or server configuration needed.**

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `BOT_TOKEN` | Telegram Bot Token from @BotFather | ✅ |
| `MONGODB_URI` | MongoDB Atlas connection string | ✅ |
| `ADMIN_USER_IDS` | Comma-separated admin user IDs | ✅ |
| `REFERRAL_REWARD` | Reward amount per referral (default: 10) | ❌ |
| `DAILY_BONUS_AMOUNT` | Daily bonus amount (default: 5) | ❌ |
| `MINIMUM_WITHDRAWAL` | Minimum withdrawal amount (default: 10) | ❌ |

**Note:** The bot uses long polling by default. No webhook URL, domain, or SSL configuration is required.

### Bot Setup

1. **Create a Telegram Bot**
   - Message @BotFather on Telegram
   - Use `/newbot` command
   - Get your bot token

2. **Setup MongoDB Atlas**
   - Create a free cluster
   - Get connection string
   - Add to `.env` file

3. **Configure Admin Access**
   - Add your Telegram user ID to `ADMIN_USER_IDS`
   - Set a secure admin password

## 📁 Project Structure

```
refer-earn-bot/
├── main.py                 # Entry point
├── config.py              # Configuration management
├── requirements.txt       # Python dependencies
├── .env.example          # Environment template
├── README.md             # Documentation
└── src/
    ├── bot.py            # Main bot class
    ├── database.py       # Database connection
    ├── models/           # Data models
    ├── handlers/         # Message handlers
    ├── utils/           # Utility functions
    ├── admin/           # Admin panel
    └── services/        # Business logic
```

## 🎯 Usage

### For Users
1. Start the bot with `/start`
2. Join required channels (if any)
3. Complete available tasks to earn rewards
4. Share your referral link
5. Claim daily bonuses


### For Admins
1. Use `/admin` command
2. Enter admin password
3. Access admin panel features
4. Create and manage tasks
5. Review task submissions
6. Manage users, withdrawals, and settings

## 🔧 Development

### Adding New Features
1. Create models in `src/models/`
2. Add handlers in `src/handlers/`
3. Implement services in `src/services/`
4. Update admin panel if needed

### Database Schema
- **Users**: Profile, balance, referrals
- **Transactions**: Earnings, withdrawals
- **Tasks**: Admin-created tasks with configurations
- **User Tasks**: Task completion tracking per user
- **Task Submissions**: Image submissions and review status
- **Channels**: Required subscriptions
- **Products**: Digital product catalog
- **Settings**: Bot configuration
- **Task Audit Logs**: Security and action tracking

## 🛡️ Security Features

- Rate limiting and spam protection
- Secure admin authentication
- Input validation and sanitization
- Error handling and logging
- Data encryption for sensitive information

## 📊 Analytics

The bot provides comprehensive analytics:
- User registration trends
- Referral performance
- Withdrawal patterns
- Popular products
- Channel subscription rates

## 🚀 Deployment

### Production Deployment
1. Use webhook mode for better performance
2. Set up SSL certificate
3. Configure reverse proxy (nginx)
4. Use process manager (PM2, systemd)
5. Set up monitoring and logging

### Docker Deployment
```bash
# Build image
docker build -t referral-bot .

# Run container
docker run -d --env-file .env referral-bot
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team

## 🔄 Updates

Check the CHANGELOG.md for latest updates and version history.

---

**Made with ❤️ for the Telegram community**
