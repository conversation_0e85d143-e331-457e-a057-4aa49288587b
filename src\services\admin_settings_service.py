import logging
from datetime import datetime, timezone
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.admin_settings import AdminSettings

logger = logging.getLogger(__name__)

class AdminSettingsService:
    """Service for managing admin configurable settings"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.collection = db.admin_settings
    
    async def get_settings(self) -> AdminSettings:
        """Get current admin settings, create default if none exist"""
        try:
            settings_data = await self.collection.find_one({})
            
            if settings_data:
                return AdminSettings.from_dict(settings_data)
            else:
                # Create default settings
                default_settings = AdminSettings()
                await self.save_settings(default_settings)
                return default_settings
                
        except Exception as e:
            logger.error(f"Failed to get admin settings: {e}")
            # Return default settings as fallback
            return AdminSettings()
    
    async def save_settings(self, settings: AdminSettings) -> bool:
        """Save admin settings to database"""
        try:
            settings_dict = settings.to_dict()
            
            # Use upsert to replace existing settings
            result = await self.collection.replace_one(
                {},  # Empty filter to match any document
                settings_dict,
                upsert=True
            )
            
            logger.info(f"Admin settings saved successfully. Modified: {result.modified_count}, Upserted: {result.upserted_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save admin settings: {e}")
            return False
    
    async def update_settings(self, admin_id: int, **kwargs) -> bool:
        """Update specific settings"""
        try:
            settings = await self.get_settings()
            settings.update_settings(admin_id, **kwargs)
            return await self.save_settings(settings)
            
        except Exception as e:
            logger.error(f"Failed to update admin settings: {e}")
            return False
    
    async def get_setting_value(self, setting_name: str, default_value=None):
        """Get a specific setting value"""
        try:
            settings = await self.get_settings()
            return getattr(settings, setting_name, default_value)

        except Exception as e:
            logger.error(f"Failed to get setting {setting_name}: {e}")
            return default_value

    async def update_setting(self, setting_name: str, value, admin_id: int = None) -> bool:
        """Update a specific setting value"""
        try:
            settings = await self.get_settings()

            # Validate setting name exists
            if not hasattr(settings, setting_name):
                logger.error(f"Setting {setting_name} does not exist")
                return False

            # Update the setting
            setattr(settings, setting_name, value)

            # Update admin tracking
            if admin_id:
                settings.updated_by = admin_id
                settings.updated_at = datetime.now(timezone.utc)

            # Save settings
            return await self.save_settings(settings)

        except Exception as e:
            logger.error(f"Failed to update setting {setting_name}: {e}")
            return False
    
    async def reset_to_defaults(self, admin_id: int) -> bool:
        """Reset all settings to default values"""
        try:
            default_settings = AdminSettings()
            default_settings.updated_by = admin_id
            return await self.save_settings(default_settings)
            
        except Exception as e:
            logger.error(f"Failed to reset settings to defaults: {e}")
            return False
